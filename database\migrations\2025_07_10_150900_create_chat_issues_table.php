<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_issues', function (Blueprint $table) {
            $table->id();
            $table->string('Name')->nullable();
            $table->date('Date')->nullable();
            $table->string('Time')->nullable();
            $table->text('Desc')->nullable();
            $table->string('Image')->nullable();
            $table->unsignedBigInteger('Issue')->nullable();
            $table->integer('Type')->default(1);
            $table->integer('Appear')->default(1);
            $table->timestamps();

            $table->foreign('Issue')->references('id')->on('issues')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_issues');
    }
}

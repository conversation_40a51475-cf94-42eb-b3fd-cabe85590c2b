@php
use App\Models\CompanyData;
// $Def is now passed from the route to avoid database queries before tenancy initialization
// If $Def is not set (for backward compatibility), try to get it
if (!isset($Def)) {
    try {
        $Def = CompanyData::orderBy('id', 'desc')->first();
    } catch (\Exception $e) {
        $Def = null;
    }
}
@endphp
<style>
.page-logo {
    width: 100% !important;
}
</style>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>
        {{ trans('admin.Login') }}
    </title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>
    <meta name="description" content="Login">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
    <!-- Call App Mode on ios devices -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <!-- Remove Tap Highlight on Windows Phone IE -->
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <!-- Place favicon.ico in the root directory -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{ URL::to($Def->Icon) }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ URL::to($Def->Icon) }}">
    <link rel="mask-icon" href="{{ URL::to($Def->Icon) }}" color="#5bbad5">
    <?php
    if (!function_exists('direction')) {
        function direction() {
            if (session()->has('lang')) {
                if (session('lang') == 'ar') {
                    return 'rtl';
                } else {
                    return 'ltr';
                }
            } else {
                return 'rtl';
            }
        }
    }
    ?>
    @if (direction() == 'ltr')
    <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/vendors.bundle.css') }}">
    <link id="appbundle" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/app.bundle.css') }}">
    <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
    <link id="myskin" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/skins/skin-master.css') }}">
    <link rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/page-login-alt.css') }}">
    <link rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/style.css') }}">
    @else
    <!-- base css -->
    <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/vendors.bundle.css') }}">
    <link id="appbundle" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/app.bundle.css') }}">
    <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
    <link id="myskin" rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/skins/skin-master.css') }}">
    <link rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/page-login-alt.css') }}">
    <link rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/style.css') }}">
    <link rel="stylesheet" media="screen, print" href="{{ asset('Admin/css/style-ar.css') }}">
    @endif
</head>
<body>
    <!-- DOC: script to save and load page settings -->
    <script>
        /**
         * This script should be placed right after the body tag for fast execution
         * Note: the script is written in pure javascript and does not depend on third-party library
         **/
        var classHolder = document.getElementsByTagName("BODY")[0],
            /**
             * Load from localstorage
             **/
            themeSettings = (localStorage.getItem('themeSettings')) ? JSON.parse(localStorage.getItem('themeSettings')) : {},
            themeURL = themeSettings.themeURL || '',
            themeOptions = themeSettings.themeOptions || '';

        /**
         * Load theme options
         **/
        if (themeSettings.themeOptions) {
            classHolder.className = themeSettings.themeOptions;
            console.log("%c✔ Theme settings loaded", "color: #148f32");
        } else {
            console.log("%c✔ Heads up! Theme settings is empty or does not exist, loading default settings...", "color: #ed1c24");
        }

        if (themeSettings.themeURL && !document.getElementById('mytheme')) {
            var cssfile = document.createElement('link');
            cssfile.id = 'mytheme';
            cssfile.rel = 'stylesheet';
            cssfile.href = themeURL;
            document.getElementsByTagName('head')[0].appendChild(cssfile);
        } else if (themeSettings.themeURL && document.getElementById('mytheme')) {
            document.getElementById('mytheme').href = themeSettings.themeURL;
        }

        /**
         * Save to localstorage
         **/
        var saveSettings = function () {
            themeSettings.themeOptions = String(classHolder.className).split(/[^\w-]+/).filter(function (item) {
                return /^(nav|header|footer|mod|display)-/i.test(item);
            }).join(' ');
            if (document.getElementById('mytheme')) {
                themeSettings.themeURL = document.getElementById('mytheme').getAttribute("href");
            };
            localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
        };

        /**
         * Reset settings
         **/
        var resetSettings = function () {
            localStorage.setItem("themeSettings", "");
        };
    </script>

    <div class="background-image">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 box-form pe-5">
                <div class="page-logo text-center">
                    <!-- Logo -->
                    <img src="https://res.cloudinary.com/dy6e9yvs9/image/upload/v1745012170/ost_erp_gglhqk.png" alt="OST ERP" aria-roledescription="logo">
                    <span class="page-logo-text mr-1">
                        @if (!empty($Def->Name))
                            {{ app()->getLocale() == 'ar' ? $Def->Name : $Def->NameEn }}
                        @else
                            {{ trans('admin.Ost') }}
                        @endif
                    </span>
                </div>
                <div class="card p-4 border-top-left-radius-0 border-top-right-radius-0">
                    <span id="ex">@include('admin.layouts.messages')</span>
                    <form action="{{ url('Login') }}" method="post" enctype="multipart/form-data">
                        {!! csrf_field() !!}
                        @honeypot
                        <div class="form-group">
                            <input type="email" name="email" id="username" class="form-control" placeholder="{{ trans('admin.Email') }}" value="{{ old('email') }}" required>
                        </div>
                        <div class="form-group">
                            <input type="password" name="password" id="password" class="form-control" placeholder="{{ trans('admin.Password') }}" value="{{ old('password') }}" required>
                        </div>
                        <div class="form-group text-left">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="rememberme" id="rememberme">
                                <label class="custom-control-label" for="rememberme">{{ trans('admin.RmemberMe') }}</label>
                            </div>
                        </div>
                        <button style="color: black;" type="submit" class="btn btn-default">{{ trans('admin.Login') }}</button>
                    </form>
                </div>
                <div class="blankpage-footer text-center">
                    <a href="{{ url('forgotpassword') }}"><strong style="color: black;">{{ trans('admin.ForgotPassword') }}</strong></a>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Match logo colors */
        .page-logo img {
            max-width: 200px; /* Adjust size as needed */
        }

        /* Form styling */
        .card {
            padding: 20px 134px !important;
        }

        .card .form-group input {
            margin: 20px 91px !important;
            text-align: center !important;
            margin: auto !important;
            border: 1px solid #574677;
        }

        .page-logo {
            width: 29.875rem;
        }

        .page-logo span {
            padding-right: 22rem;
        }

        .btn-login {
            background-color: #695191 !important;
            color: white !important;
        }

        .box-form {
            margin: auto !important;
            text-align: center !important;
            width: 100% !important;
        }

        /* Media query for smaller screens */
        @media (max-width: 600px) {
            .box-form {
                padding: 0 30px 400px 30px !important;
                margin: auto !important;
            }

            .page-logo span {
                padding-right: 17rem;
            }

            .page-logo img {
                padding-right: 0px;
            }
        }

        /* Background color to match logo theme */
        body {
            background-color: #f8f9fa; /* Light background to complement logo colors */
        }
    </style>

    <!-- Page related scripts -->
    <script src="{{ asset('Admin/js/vendors.bundle.js') }}"></script>
    <script src="{{ asset('Admin/js/app.bundle.js') }}"></script>
    <script>
        $(document).ready(function () {
            setTimeout(function () { $("#ex").hide(); }, 6000);
        });
    </script>
</body>
</html>

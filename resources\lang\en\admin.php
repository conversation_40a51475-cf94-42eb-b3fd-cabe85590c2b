<?php
// Dynamic translations from database - moved to prevent database query issues
// This will be handled by a translation service or helper function
$x = [];

        return [

'OST'=>'Ostegy ERP',
'incorrect_information_login'=>'Incorrect  Email or Password ',
'Admins'=>'Admins',
'SalesBills'=>'Sales Bills',
'AddAdmin'=>'Add Admin',
'Name'=>'Name',
'Email'=>'Email',
'Already_Done'=>'Already Done',
'Permations'=>'Permations',
'ExceptProductProfits'=>'Except Product Profits',
'Actions'=>'Actions',
'PurchasesBills'=>'Purchases Bills',            
'StoresMovesReport'=>'Stores Moves Report',            
'StoresTransferReport'=>'Stores Transfer Report',            
'SafesTransferReport'=>'Safes Transfer Report',            
'CompareSalesPrice'=>'Compare Sales Price',            
'ProductMoveDetails'=>'Product Move Details',            
'MostSalesProducts'=>'Most Sales Products',            
'ProfitSalesProduct'=>'Profit Sales Product',            
'WorkNumberReports'=>'Work Number Reports',            
'EmpSales'=>'Employess Sales',            
'ClientAccountStatement'=>'Client Account Statement Details',            
'ClientsAccountStatement'=>'Clients Account Statement',            
'VendorAccountStatement'=>'Vendor Account Statement Details',            
'VendorsAccountStatement'=>'Vendors Account Statement',            
'EmpGoals'=>'Employees Goals',            
'SubIncomList'=>'Sub Incom List',            
'ExpensesList'=>'Expenses List',     
'Address_Name'=>'Address Name',
'Status'=>'Status',
'Image'=>'Image',
'Branches'=>'Branches',
'Branch'=>'Branch',
'DeviceDescrips'=>'Device Descriptions',
'TermsMaintaince'=>'Terms and Conditions Maintaince',
'Appear'=>'Appear',
'Not_Work'=>'Not Work',
'Work'=>'Work',
'Pass'=>'Password',
'Eng'=>'Engineer',
'Reason'=>'Reason',
'Reported'=>'Reported',
'Refused'=>'Refused',
'New'=>'New',
'POS'=>'POS',
'Cash_Visa'=>'Cash and Visa',
'Brands'=>'Brands',
'Close_Shift'=>'Close Shift',
'Total_Cash'=>'Total Cash',
'Total_Later'=>'Total Later',
'Total_Visa'=>'Total Visa',
'Shift_Details'=>'Shift Details',
'Close_Shift'=>'Close Shift',
'Shift_Code'=>'Shift Code',
'Open_Shift'=>'Open Shift',
'New_Shift'=>'New Shift',
'Open_New_Shift_Successfully'=>'Open New Shift Successfully',
'Shift_Pass'=>'Shift Password',
'Edited'=>'Edited',
'SalesPetrol'=>'Sales Petrol',
'PayIncomChecks'=>'Pay Incom Checks',
'RefuseIncomChecks'=>'Refuse Incom Checks',
'RefuseExportChecks'=>'Refuse Export Checks',
'PayExportChecks'=>'Pay Export Checks',
'No_Data_Find'=>'No Data Find',
'Total_Fixed_Assets'=>'Total Fixed Assets',
'Total_Current_Assets'=>'Total Current Assets',
'Total_Other_Assets'=>'Total Other Assets',
'Total_Assets'=>'Total  Assets',
'Total_Liabilities'=>'Total  Liabilities',
'Total_longterm_liabilities'=>'Total long-term liabilities',
'Total_shortterm_liabilities'=>'Total short-term liabilities',
'Working_Capital'=>'Working Capital',
'Total_Investment'=>'Total Investment',
'Net_Profit_Loss'=>'Net Profit Loss',
'Total_Rights'=>'Total Rights',
'Fixed_Assets'=>'Fixed Assets',
'Traded_Assets'=>'Traded Assets',
'Other_Assets'=>'Other Assets',
'Sales_Net'=>'Sales Net',
'Total_Expenses'=>'Total Expenses',
'Total_Sales_Cost'=>'Total Sales Cost',
'Total_Profit_Loss'=>'Total Profit Loss',
'Total_Revenue'=>'Total Revenue',
'shortterm_opponents'=>'shortterm opponents',
'Dif_Between_Them'=>'Differnce Between Them',
'Total_Rights_longterm'=>'Ownership rights and long-term liabilities',
'New_Items_Guide'=>'New Items Guide',
'Choice_Unit'=>'Choice Unit',
'Price_Last_Purch_Bill'=>'Price Last Purchases Bill',
'Price_Last_Purch_Bill_from_this_Vendor'=>'Price Last Purchases Bill from This Vendor',
'Other_Stores_Qty'=>'Other Stores Qties',
'Trans_To_Sales_Bill'=>'Transfer To Sales Bill',
'Trans_To_Purchases_Bill'=>'Transfer To Purchases Bill',
'ThisNameAlreadyExist'=>'This Name Already Exist',
'Transfer_To_Purch'=>'Transfer To Purchase Bill',
'Quality'=>'Quality',
'Purch_Order_Qty'=>'Purchases Order Qty',
'Qty_Note'=>'Qty Note',
'Quality_Note'=>'Quality Note',
'Quality_Done'=>'Quality Done',
'Barcode_Print'=>'Barcode Print',
'Arabic_Problem'=>'Arabic Problem',
'English_Problem'=>'English Problem',
'Arabic_Comment'=>'Arabic Comment',
'English_Comment'=>'English Comment',
'Expenses'=>'Expenses',
'Cashing'=>'Cashing',
'7_Days'=>'7 Days',
'14_Days'=>'14 Days',
'21_Days'=>'21 Days',
'2_Month'=>'2 Month',
'3_Month'=>'3 Month',
'4_Month'=>'4 Month',
'5_Month'=>'5 Month',
'6_Month'=>'6 Month',
'Year'=>'Year',
'Total_Pay_With_Delivery'=>'Total Pay With Delivery',
'2_Year'=>'2 Year',
'InstallmentCollect'=>'Installment Collect',
'Premission_To_GoodsOut'=>'Premission To Goods Out',
'Later_Collection'=>'Later Bill Collection',
'Recipt_Voucher'=>'Recipt Voucher',
'Client_Voucher'=>'Client Voucher',
'Payment_Voucher'=>'Payment Voucher',
'Expenses_Voucher'=>'Expenses Voucher',
'Vendor_Voucher'=>'Vendor Voucher',
'Add_Borrow'=>'Add Borrow',
'Products_Daily'=>'Products Daily',
'Open_Safe_Drawer'=>'Open Safe Drawer',
'Cant_Discount_This_Amount_From_That_Product'=>'Cant Discount This Amount From That Product',
'Not_Enough_Qty'=>'Not Enough Qty',
'Password_To_Open_SafeDrawer'=>'Password To Open Safe Drawer',
'Qty_In_OtherStores'=>'Qty In Other Stores',
'Checkout'=>'Checkout',
'financial_management'=>'Financial Management',
'store_management'=>'Store Management',
'purchases_management'=>'Purchases Management',
'sales_management'=>'Sales Management',
'hr_management'=>'HR Management',
'manufacturing_management'=>'Manufacturing Management',
'producation_management'=>'producation Management',
'quality_management'=>'Quality Management',
'higher_management'=>'Higher Management',
'DUWELTS'=>'Do you want to enter the lost to the store?',
'Release'=>'Release',
'Depends'=>'Depends',
'Quality_Observer'=>'Quality Observer',
'Recipient'=>'Recipient',
'Ar_Desc_Work'=>'Arabic Terms if  Work',
'En_Desc_Work'=>'English Terms if  Work',
'Ar_Desc_Not_Work'=>'Arabic Terms if  Not Work',
'En_Desc_Not_Work'=>'English Terms if  Not Work',




            







            





            
        
'ExchangeCommissionsSechdule'=>'Exchange Commissions Sechdule',
'ExchangeCommissions'=>'Exchange Commissions',
'Return_Maintaince'=>'Return Maintaince',
'Commision'=>'Commision',
'Total_Commision'=>'Total Commision',
'Total_Exchange_Commision'=>'Total Exchange Commision',
'Prev_Exchange_Commision'=>'Previous Exchange Commision',
'ReturnMaintainceBillSechdule'=>'Return Maintaince Bill Sechdule',
'ReturnMaintainceBill'=>'Return Maintaince Bill',
'Cost_Precent_Eng'=>'Cost Precent Engineer',
'ReturnMaintainceBill'=>'Return Maintaince Bill',
'Clients_Group'=>'Clients Group',
'TransEngReciptMaintaince'=>'Transfer to other Engineer Recipt Maintaince',
'RefuseReciptMaintaince'=>'Refuse Recipt Maintaince',
'Recipient'=>'Recipient',
'ReciptMaintainceSechduleEng'=>'Recip tMaintaince Sechdule (Engineer)',
'Device_Work'=>'Device Working',
'Client_Signture'=>'Client Signture',
'SureReciptMaintaince'=>'Sure Recipt Maintaince',
'Pattern_Image'=>'Pattern Image',
'Desc_Work'=>'Terms and Condition (Work)',
'Desc_Not_Work'=>'Terms and Condition (Not Work)',
'Expire'=>'Expire',
'Password'=>'Password',
'Total_Goods_Sales'=>'Total Goods Sales',
'ConfirmPassword'=>'Confirm Password',
'Add'=>'Add',
'Close'=>'Close',
'nameRequired'=>'Name Field is Required',
'emailRequired'=>'Email Field is Required',
'emailEmail'=>'Email Field Should <NAME_EMAIL>',
'emailUnique'=>'Email is Already Taken',
'passwordRequired'=>'Password Field is Required',
'passwordmin_6'=>'Password Should to be at least 6 Number',
'Edit'=>'Edit',
'SaveChanges'=>'Save Changes',
'Updated'=>'Updated',
'RUSWDT'=>'Are You Sure Want Delete This ',
'Yes'=>'Yes',
'NO'=>'No',
'Deleted'=>'Deleted',
'Delete'=>'Delete',
'NotSelected'=>'You Not Selected Any Column',
'Super'=>'Super',
'UnSuper'=>'UnSuper',
'NewAddAdmin'=>'New Admin Added',
'Slider'=>'Slider',
'WebSlider'=>'Web Slider',
'AddNewSlider'=>'Add New Slider',
'Arabic_Title'=>'Arabic Title',
'English_Title'=>'English Title',
'Arabic_Desc'=>'Arabic Description',
'English_Desc'=>'English Description',
'Active'=>'Active',
'UnActive'=>'Un Active',
'Action'=>'Action',
'NewAddSlider'=>'New Slider Added',
'UnActivited'=>'Un Activited',
'Activited'=>'Activited',
'Arabic_DescRequired'=>'Arabic Description Field is Required',
'English_DescRequired'=>'English Description Field is Required',
'English_TitleRequired'=>'English Title Field is Required',
'Arabic_TitleRequired'=>'Arabic Title Field is Required',
'ImageRequired'=>'Image is Required',
'EditSlider'=>'Edit Slider',
'Skills'=>'Our Skills',
'HomeSite'=>'Home Page Website',
'Skill'=>'Skill',
'Degree'=>'Degree',
'Edit'=>'Edit',
'DegreeRequired'=>'Degree Field is Required',
'SkillRequired'=>'Skill Field is Required',
'Image_Arabic_TitleRequired'=>'Image Arabic Title Field is Required',
'Image_English_TitleRequired'=>'Image English Title Field is  Required',
'Image_Arabic_DescRequired'=>'Image Arabic Description Field is   Required',
'Image_English_DescRequired'=>'Image English Description Field is  Required',
'AboutKlar'=>'About Klar',
'About_Klar'=>'About Klar',
'Image_Arabic_Title'=>'Image Arabic Title',
'Image_English_Title'=>'Image English Title',
'Image_Arabic_Desc'=>'Image Arabic Description',
'Image_English_Desc'=>'Image English Description',
'GalleryDepartment'=>'Gallery Department',
'NewAddDepartment'=>'New Department Added',
'Department'=>'Department',
'GalleryDepartment'=>'Gallery Department',
'AddNewGalleryDepartment'=>'Add New Gallery Department',
'EditGalleryDepartment'=>'Edit Gallery Department',
'Gallery'=>'Gallery',
'AddNewImage'=>'Add New Image',
'Arabic_SubTitle'=>'Arabic SubTitle',
'English_SubTitle'=>'English SubTitle',
'Arabic_SubTitleRequired'=>'Arabic SubTitle Field is Required',
'English_SubTitleRequired'=>'English SubTitle Field is Required',
'DepartmentRequired'=>'Department Field is Required',
'NewAddImage'=>'New Image Added',
'Customer'=>'Customer',
'Testimonial'=>'Testimonial',
'Customers'=>'Customers',
'NameRequired'=>'Name Field is Required',
'NewAddCustomer'=>'New Customer Added',
'NewAddTestimonial'=>'New Testimonial Added',
'AddNewTestimonial'=>'Add New Testimonial',
'AddNewCustomers'=>'Add New Customer',
'About'=>'About',
'ContactUS'=>'Contact Us',
'NewAddInfo'=>'New Information Added',
'AddNewInfo'=>'Add New Information',
'Flag'=>'Flag',
'Location'=>'Location',
'Phone1'=>'Phone 1',
'Phone2'=>'Phone 2',
'Name'=>'Name',
'addressofmap'=>'Address of Map',
'AddNewContactUs'=>'Add New Contact US',
'ContactUs'=>'Contact US',
'FlagRequired'=>'Flag Field is Required',
'LatRequired'=>'Latitude Field is Required',
'LongRequired'=>'Longtitude Field is Required',
'addressRequired'=>'Address Field is Required',
'PhoneRequired'=>'Phone Field is Required',
'PhoneNumeric'=>'Phone Field Should be a Numeric',
'EmailRequired'=>'Email Field is Required',
'Emailemail'=>'Email Field <NAME_EMAIL>',
'NameRequired'=>'Name Field is Required',
'Co'=>'Co',
'Messages'=>'Messages',
'DeleteAll'=>'Delete All',
'Subject'=>'Subject',
'Message'=>'Message',
'Subscribe'=>'Subscribe',
'Subscribes'=>'Subscribes',
'SendEmail'=>'Send Email',
'Emails'=>'Emails',
'Send'=>'Send',
'MessageRequired'=>'Message Field is Required',
'email_sent'=>'Email Sent',
'JoinUs'=>'Join Us',
'JoinUS'=>'Join Us',
'Phone'=>'Phone',
'BirthDate'=>'BirthDate',
'Nationality'=>'Nationality',
'Religion'=>'Religion',
'Marital_Status'=>'Marital Status',
'Job_Type'=>'Job Type',
'CV'=>'CV',
'Show'=>'Show',
'Download'=>'Download',
'Settings'=>'Settings',
'Arabic_Website'=>'Arabic Website',
'English_Website'=>'English Website',
'Arabic_Address'=>'Arabic Address',
'English_Address'=>'English Address',
'Phone1'=>'Phone 1',
'Phone2'=>'Phone 2',
'Email'=>'Email',
'Map_Address'=>'Address on Map',
'Logo'=>'Logo',
'Icon'=>'Icon',
'Arabic_WebsiteRequired'=>'Arabic Website Field is Required',
'English_WebsiteRequired'=>'English Website Field is Required',
'Arabic_AddressRequired'=>'Arabic Address Field is Required',
'English_AddressRequired'=>'English Address Field is Required',
'LongRequired'=>'Longtitude Field is  Required',
'LatRequired'=>'Latitude Field is Required',
'Map_AddressRequired'=>'Address on Map Field is Required Required',
'AboutUs'=>'About Us',
'AboutUsInAr'=>'About Us In Arabic',
'AboutUsInEn'=>'About Us In English',
'TeamWork'=>'Team Work',
'AddNewTeamMember'=>'Add New Team Member',
'Job_Type'=>'Job Type',
'NewAddTeamMember'=>'New  Team Member Added ',
'Job_TypeRequired'=>'Job Type Field is Required',
'SocialMedia'=>'Social Media',
'Facebook'=>'Facebook',
'Twitter'=>'Twitter',
'Instagram'=>'Instagram',
'Youtube'=>'Youtube',
'Snapchat'=>'Snapchat',
'Whatsapp'=>'Whatsapp',
'Google_Plus'=>'Google Plus',
'LinkedIn'=>'LinkedIn',
'Pinterest'=>'Pinterest',
'Telegram'=>'Telegram',
'iOS'=>'iOS',
'Android'=>'Android',
'Dashbourd'=>'Dashbourd',
'Show'=>'Show',
'AddNewService'=>'Add New Service',
'NewAddService'=>'New  Service Added',
'Services'=>'Services',
'Pricing'=>'Our Pricing',
'Desc1'=>'Description 1',
'Desc2'=>'Description 2',
'Desc3'=>'Description 3',
'Desc4'=>'Description 4',
'Desc5'=>'Description 5',
'Desc6'=>'Description 6',
'Arabic_Offer_TitleRequired'=>'Arabic Offer Title Field is Required',
'English_Offer_TitleRequired'=>'English Offer Title Field is Required',
'PriceRequired'=>'Price Field is Required',
'Desc1Required'=>'Description 1 Field Required',
'Desc2Required'=>'Description 2 Field Required',
'Desc3Required'=>'Description 3 Field Required',
'Desc4Required'=>'Description 4 Field Required',
'Desc5Required'=>'Description 5 Field Required',
'Desc6Required'=>'Description 6 Field Required',
'Arabic_Offer_Title'=>'Arabic Offer Title',
'English_Offer_Title'=>'English Offer Title',
'Price'=>'Price',
'Products'=>'Products',
'AddNewProducts'=>'Add New Products',
'File'=>'File',
'FileRequired'=>'File Field is Required',
'NewAddProducts'=>'New Product Added',
'Articles'=>'Blogs',
'Article'=>'Blog',
'ArticlesCategory'=>'Blogs Category',
'AddNewArticlesCategory'=>'Add New Blog Category',
'EditArticlesCategory'=>'Edit Blog Category',
'NewAddCategory'=>'New Category Added',
'AddNewArticle'=>'Add New Blog',
'Category'=>'Category',
'Day'=>'Day',
'Month'=>'Month',
'Year'=>'Year',
'Author'=>'Author',
'January'=>'January',
'February'=>'February',
'March'=>'March',
'April'=>'April',
'May'=>'May',
'June'=>'June',
'July'=>'July',
'August'=>'August',
'September'=>'September',
'October'=>'October',
'November'=>'November',
'December'=>'December',
'AuthorRequired'=>'Author Field is Required',
'DayRequired'=>'Day Field is Required',
'MonthRequired'=>'Month Field is Required',
'YearRequired'=>'Year Field is Required',
'CategoryRequired'=>'Category Field is Required',
'NewAddArticle'=>'New Article Added',
'Times'=>'Times',
'TimesRequired'=>'Times Field is Required',
'Contact_Us'=>'Contact Us',
'Social_Media'=>'Social Media',
'About_Us'=>'About Us',
'Team_Work'=>'Team Work',
'Settings'=>'Settings',
'Web_Slider'=>'Web Slider',
'Our_Skills'=>'Our Skills',
'Subscribes'=>'Subscribes',
'Join_Us'=>'Join Us',
'Gallery'=>'Gallery',
'Domain_Regstration'=>'Domain Regstration',
'Gallery_Department'=>'Gallery Department',
'Products'=>'Products',
'domainRequired'=>'Domain is  Required',
'domainUnique'=>'Domain is  Unique',
'Services'=>'Services',
'Article'=>'Blogs',
'Articles_Category'=>'Blogs Category',
'About_Klar'=>'About Klar',
'Our_Pricing'=>'Our Pricing',
'Testimonial'=>'Testimonial',
'Customers'=>'Customers',
'Messages'=>'Messages',
'DegreeRequired'=>'Degree Field is Required',
'Arabic_SubTitleRequired'=>'Arabic SubTitle Field is Required',
'English_SubTitleRequired'=>'English SubTitle Field is Required',
'Facts'=>'Facts',
'Titles'=>'Titles',
'About_Klar_Arabic_TitleRequired'=>'About_Klar_Arabic_Title Field is Required',
'About_Klar_English_TitleRequired'=>'About_Klar_English_Title Field is Required',
'About_Klar_Arabic_DescRequired'=>'About_Klar_Arabic_Field_Description is Required',
'About_Klar_English_DescRequired'=>'About_Klar_English_Field_Description is Required',
'Facts_English_DescRequired'=>'Facts_English_Description Field is Required',
'Facts_Arabic_DescRequired'=>'Facts_Arabic_Description Field is Required',
'Our_Works_Arabic_TitleRequired'=>'Our_Works_Arabic_Title Field is Required',
'Our_Works_English_TitleRequired'=>'Our_Works_English_Title Field is Required',
'Our_Works_Arabic_DescRequired'=>'Our_Works_Arabic_Description Field is Required',
'Our_Works_English_DescRequired'=>'Our_Works_English_Description Field is Required',
'Happy_Clients_English_DescRequired'=>'Happy_Clients_English_Description Field is Required',
'Happy_Clients_Arabic_DescRequired'=>'Happy_Clients_Arabic_Description Field is Required',
'News_and_Event_Arabic_DescRequired'=>'News_and_Event_Arabic_Description Field is Required',
'News_and_Event_English_DescRequired'=>'News_and_Event_English_Description Field is Required',
'News_and_Event_Arabic_TitleRequired'=>'News_and_Event_Arabic_Title Field is Required',
'News_and_Event_English_TitleRequired'=>'News_and_Event_English_Title Field is Required',
'Stay_in_Touch_English_DescRequired'=>'Stay_in_Touch_English_Description Field is Required',
'Stay_in_Touch_Arabic_DescRequired'=>'Stay_in_Touch_Arabic_Description Field is Required',
'Contact_Us_Arabic_DescRequired'=>'Contact_Us_Arabic_Description Field is Required',
'Contact_Us_English_DescRequired'=>'Contact_Us_English_Description Field is Required',
'Meet_Team_Arabic_DescRequired'=>'Meet_Team_Arabic_Description Field is Required',
'Meet_Team_English_DescRequired'=>'Meet_Team_English_Description Field is Required',
'Meet_Team_Arabic_TitleRequired'=>'Meet_Team_Arabic_Title Field is Required',
'Meet_Team_English_TitleRequired'=>'Meet_Team_English_Title Field is Required',
'Our_Pricing_Arabic_DescRequired'=>'Our_Pricing_Arabic_Description Field is Required',
'Our_Pricing_English_DescRequired'=>'Our_Pricing_English_Description Field is Required',
'Our_Pricing_Arabic_TitleRequired'=>'Our_Pricing_Arabic_Title Field is Required',
'Our_Pricing_English_TitleRequired'=>'Our_Pricing_English_Title Field is Required',
'Products_Arabic_TitleRequired'=>'Products_Arabic_Title Field is Required',
'Products_English_TitleRequired'=>'Products_English_Title Field is Required',
'Join_Us_Arabic_TitleRequired'=>'Join_Us_Arabic_Title Field is Required',
'Join_Us_English_TitleRequired'=>'Join_Us_English_Title Field is Required',
'About_Klar_Arabic_Title'=>'About Klar Arabic Title',
'About_Klar_English_Title'=>'About Klar English Title',
'About_Klar_Arabic_Desc'=>'About Klar Arabic Descrtiption',
'About_Klar_English_Desc'=>'About Klar English Descrtiption',
'Facts_English_Desc'=>'Facts English Descrtiption',
'Facts_Arabic_Desc'=>'Facts Arabic Descrtiption',
'Our_Works_Arabic_Title'=>'Our Works Arabic Title',
'Our_Works_English_Title'=>'Our Works English Title',
'Our_Works_Arabic_Desc'=>'Our Works Arabic Descrtiption',
'Our_Works_English_Desc'=>'Our Works English_Descrtiption',
'Happy_Clients_English_Desc'=> 'Happy Clients English Descrtiption',
'Happy_Clients_Arabic_Desc'=>'Happy Clients Arabic Descrtiption',
'News_and_Event_Arabic_Desc'=>'News and Event Arabic Descrtiption',
'News_and_Event_English_Desc'=>'News and Event English Descrtiption',
'News_and_Event_Arabic_Title'=>'News and Event Arabic Title',
'News_and_Event_English_Title'=>'News and Event English Title',
'Stay_in_Touch_English_Desc'=>'Stay in Touch English Descrtiption',
'Stay_in_Touch_Arabic_Desc'=>'Stay in Touch Arabic Descrtiption',
'Contact_Us_Arabic_Desc'=>'Contact Us Arabic Descrtiption',
'Contact_Us_English_Desc'=>'Contact Us English Descrtiption',
'Meet_Team_Arabic_Desc'=>'Meet Team Arabic Descrtiption',
'Meet_Team_English_Desc'=>'Meet Team English Descrtiption',
'Meet_Team_Arabic_Title'=>'Meet Team Arabic Title',
'Meet_Team_English_Title'=>'Meet Team English Title',
'Our_Pricing_Arabic_Desc'=>'Our Pricing Arabic Desc',
'Our_Pricing_English_Desc'=>'Our Pricing English Desc',
'Our_Pricing_Arabic_Title'=>'Our Pricing Arabic Title',
'Our_Pricing_English_Title'=>'Our Pricing English Title',
'Products_Arabic_Title'=>'Products Arabic Title',
'Products_English_Title'=>'Products English Title',
'Join_Us_Arabic_Title'=>'Join Us Arabic Title',
'Join_Us_English_Title'=>'Join Us English Title',
'login'=>'Login',
'Login'=>'Login',
'RmemberMe'=>'Rmember Me',
'ForgotPassword'=>'Forgot Password',
'Reset'=>'Reset',
'WrongEmail'=>'No Email Address Like That',
'Reset_Password'=>'Sent Email for Reset Your Password',
'ResetPassword'=>'Reset Password',
'Profile'=>'Profile',
'Delete_All'=>'Delete All',
'MobSlider'=>'Mobile Sliders',
'RUSWDTAll'=>'Are You Sure Want Delete All Selected items',
'Home'=>'Home',
'IconLogo'=>'Icon and Logo',
'Icon_Admin'=>'Admin Panel Icon',
'Icon_Site'=>'Website Icon',
'Logo_Admin'=>'Admin Panel Logo',
'Logo_Site'=>'Website Logo',
'Privacy_Policy'=>'Privacy Policy',
'Terms'=>'Terms & Conditions',
'Arabic_PrivacyRequired'=>'Privacy Policy in Arabic is Required',
'English_PrivacyRequired'=>'Privacy Policy in English is Required',
'Arabic_TermsRequired'=>'Terms and Conditions in Arabic is Required',
'English_TermsRequired'=>'Terms and Conditions in English is Required',
'Arabic_Privacy'=>'Privacy Policy in Arabic',
'English_Privacy'=>'Privacy Policy in English',
'Arabic_Terms'=>'Terms and Conditions in Arabic',
'English_Terms'=>'Terms and Conditions in English',
'Faq'=>'FAQ',
'FAQ'=>'FAQ',
'AddNewQuestion'=>'Add New Question',
'Arabic_Questions'=>'Question in Arabic',
'English_Questions'=>'Question in English',
'Arabic_Answer'=>'Answer in Arabic',
'English_Answer'=>'Answer in English',
'Arabic_QuestionsRequired'=>'Question in Arabic is Required',
'English_QuestionseRequired'=>'Question in English is Required',
'Arabic_AnswerRequired'=>'Answer in Arabic is Required',
'English_AnswerRequired'=>'Answer in English is Required',
'Footer'=>'Footer',
'AArabic_Caption1Required'=>'Arabic Caption 1 is Required',
'English_Caption1Required'=>'English Caption 1 is Required',
'AArabic_Caption2Required'=>'Arabic Caption 2 is Required',
'English_Caption2Required'=>'English Caption 2 is Required',
'Logo'=>'Logo',
'Image'=>'Image',
'Arabic_Caption1'=>'Arabic Caption 2',
'English_Caption1'=>'English_Caption 1',
'Arabic_Caption2'=>'Arabic Caption 2',
'English_Caption2'=>'English Caption 2',
'About_Us'=>'About Us',
'Hoor'=>'Hoor',
'Arabic_Desc'=>'Arabic Description',
'English_Desc'=>'English Description',
'Arabic_Vision'=>'Vision in Arabic',
'English_Vision'=>'Vision in English',
'Arabic_Mission'=>'Mission in Arabic',
'English_Mission'=>'Mission in English',
'Arabic_DescRequired'=>'Arabic Description is Required',
'English_DescRequired'=>'English Description is Required',
'Arabic_VisionRequired'=>'Vision in Arabic is Required',
'English_VisionRequired'=>'Vision in English is Required',
'Arabic_MissionRequired'=>'Mission in Arabic is Required',
'English_MissionRequired'=>'Mission in English is Required',
'Pros_Hoor'=>'Pros Hoor',
'Arabic_DescRequired'=>'Arabic Description is Required',
'English_DescRequired'=>'English Description is Required',
'Arabic_TitleRequired'=>'Arabic Title is Required',
'English_TitleRequired'=>'English Title is Required',
'Image_Arabic_SubTitle1Required'=>'Image Arabic Sub Title 1 is Required',
'Image_English_SubTitle1Required'=>'Image English Sub Title 1 is Required',
'Image_Arabic_SubDesc1Required'=>'Image Arabic Sub Description 1 is Required',
'Image_English_SubDesc1Required'=>'Image English  Sub Description 1 is Required',
'Image_Arabic_SubTitle2Required'=>'Image Arabic Sub Title 2 is Required',
'Image_English_SubTitle2Required'=>'Image English Sub Title 2 is Required',
'Image_Arabic_SubDesc2Required'=>'Image Arabic Sub Description 2 is Required',
'Image_English_SubDesc2Required'=>'Image English  Sub Description 2 is Required',
'Image_Arabic_SubTitle3Required'=>'Image Arabic Sub Title 3 is Required',
'Image_English_SubTitle3Required'=>'Image English Sub Title 3 is Required',
'Image_Arabic_SubDesc3Required'=>'Image Arabic Sub Description 3 is Required',
'Image_English_SubDesc3Required'=>'Image English  Sub Description 3 is Required',
'Image_Arabic_SubTitle4Required'=>'Image Arabic Sub Title 4 is Required',
'Image_English_SubTitle4Required'=>'Image English Sub Title 4 is Required',
'Image_Arabic_SubDesc4Required'=>'Image Arabic Sub Description 4 is Required',
'Image_English_SubDesc4Required'=>'Image English  Sub Description 4 is Required',
'Arabic_Title'=>'Arabic Title',
'English_Title'=>'English Title',
'Arabic_Desc'=>'Arabic Description',
'English_Desc'=>'English Description',
'Image1'=>'Image 1',
'Image_Arabic_SubTitle1'=>'Image Arabic Sub Title 1',
'Image_English_SubTitle1'=>'Image English Sub Title 1',
'Image_Arabic_SubDesc1'=>'Image Arabic Sub Description 1',
'Image_English_SubDesc1'=>'Image English Sub Description 1',
'Image2'=>'Image 2',
'Image_Arabic_SubTitle2'=>'Image Arabic Sub Title 2',
'Image_English_SubTitle2'=>'Image English Sub Title 2',
'Image_Arabic_SubDesc2'=>'Image Arabic Sub Description 2',
'Image_English_SubDesc2'=>'Image English Sub Description 2',
'Image3'=>'Image 3',
'Image_Arabic_SubTitle3'=>'Image Arabic Sub Title 3',
'Image_English_SubTitle3'=>'Image English Sub Title 3',
'Image_Arabic_SubDesc3'=>'Image Arabic Sub Description 3',
'Image_English_SubDesc3'=>'Image English Sub Description 3',
'Image4'=>'Image 4',
'Image_Arabic_SubTitle4'=>'Image Arabic Sub Title 4',
'Image_English_SubTitle4'=>'Image English Sub Title 4',
'Image_Arabic_SubDesc4'=>'Image Arabic Sub Description 4',
'Image_English_SubDesc4'=>'Image English Sub Description 4',
'Categories'=>'Categories',
'Added_Successfully'=>'Added Successfully',
'AddNew'=>'New Add',
'Domain'=>'Domain',
'TotalNet'=>'Total Asset Net',
'Path'=>'Path',
'TotalEhlak'=>'Total Depreciation',
'Total_Salaries'=>'Total Salaries',
'Total_Expenses'=>'Total Expenses',
'ProfitGroupsReport'=>'Profit Groups Report',
'InsurancePaperReport'=>'Insurance Paper Report',
'Show_Group'=>'Show Group',
'NoShiftOpen'=>'No Shift Open',
'Addtional_Link'=>'Addtional Link',
'Competitors'=>'Competitors',
'Updates'=>'Updates',
'ReturnSend_Recipt_Sales'=>'Return Send Recipt Sales',
'Recipt_Sales_Sent'=>'Recipt Sales Sent',
'ReSend_Bill'=>'ReSend Bill',
'ReSend_Recipt'=>'ReSend Recipt',
'Send_Recipt_Sales'=>'Send Recipt Sales',
'Note_POS'=>'Note POS',
'Sales_Code'=>'Sales Code',
'production_quantity'=>'production quantity',
'ProducationPoints'=>'Production Points',
'Purchases_Code'=>'Purchases Code',
'Point'=>'Points',
'Pigmented_permission'=>'Pigmented permission',
'Pigment_weight'=>'Pigment weight',
'net_weight'=>'net weight',
'difference'=>'difference',
'weight'=>'weight',
'total_weight'=>'total weight',
'client_delivery'=>'client delivery',
'System'=>'System',
'Start_Again'=>'Start Again',
'PlsReSub'=>'Please renew your subscription',
'Open_Drawer'=>'Open Drawer',
'CantDeleteThisItem'=>'This item cannot be deleted because it is linked to other items. For deletion, all items associated with it must be deleted first',
'ReturnWithoutBill'=>'Return Without Bill',
'Contact'=>'Contact Us',
'EmailRequired'=>'Email is Required',
'Emailemail'=>'Email Should <NAME_EMAIL>',
'Phone1Required'=>'Phone 1 is Required',
'Phone1numeric'=>'Phone 1 Should be a Number',
'Phone2Required'=>'Phone 2 is Required',
'Phone2numeric'=>'Phone 2 Should be a Number',
'Phone1'=>'Phone 1',
'Phone2'=>'Phone 2',
'AddressRequired'=>'Address is Required',
'Address'=>'Address',
'Messages_Salon'=>'Messages Salons and Makeuo Artists',
'Tax'=>'Tax',
'Arabic_NameRequired'=>'Arabic Name is Required',
'English_NameRequired'=>'English Name is Required',
'SortRequired'=>'Sort is Required',
'ValueRequired'=>'Value is Required',
'Valuenumeric'=>'Value Should be a Number',
'Bills'=>'Bills',
'Static_Rate'=>'Percent',
'Number'=>'Number',
'Arabic_Name'=>'Arabic Name',
'English_Name'=>'English Name',
'Sort'=>'Sort',
'Value'=>'Value',
'Arabic_Explain_ServiceRequired'=>'Arabic Explain Service is Required',
'English_Explain_ServiceRequired'=>'English Explain Service is Required',
'CodeRequired'=>'Code is Required',
'Gifts'=>'Gifts',
'Arabic_Explain_Service'=>'Arabic Explain Service',
'English_Explain_Service'=>'English Explain Service',
'Code'=>'Code',
'Hoor_Gifts'=>'Hoor Gifts',
'CodeUnique'=>'This Code is Already Taken',
'Download_App'=>'Download Application',
'CONTACTUS'=>'Contact Us',
'About_Hoor'=>'About Hoor',
'Useful_Links'=>'Useful Links',
'CATEGORIES'=>'Categories',
'Featured_Salons'=>'Featured Salons',
'Offers'=>'Offers',
'Beauty_Experts'=>'Beauty Experts',
'Blog'=>'Blogs',
'CONT'=>'Contact Us',
'LOGIN'=>'Login',
'Create_Account'=>'Create Account',
'Visionn'=>'Our Vision',
'Missionn'=>'Our Mission',
'Soon'=>'Soon',
'Phone_Number'=>'Phone Number',
'Full_Name'=>'Full Name',
'Write_Your_Message'=>'Write Your Message',
'Welcome'=>'Welcome',
'Looking_for_Service'=>'Looking for Service',
'Service_Provider'=>'Service Provider',
'Do_U_Have_Account_Log_in'=>'Do You Have an Account ?  Login',
'Accepted_Terms_and_Policy'=>'Accepted The Terms and Conditions and Privacy Policy',
'Please_be_Sure_Email'=>'Please make sure that the email is typed correctly because an activation message will be sent',
'Please_Write_Code'=>'Please type the code sent to the email',
'phoneRequired'=>'Phone is Required',
'phoneNumeric'=>'Phone Number Sholud be a Number',
'phoneUnique'=>'This Phone Number is Already Taken',
'Verify_Code'=>'Verify Code',
'Should_to_Accepted'=>'You Should to Accepted Terms and Conditions and Privacy Policy',
'The_Activation_code_has_been_sent_to_the_email'=>'The Activation Code has been sent to the Email',
'Send_Again'=>'Send Again',
'Sure'=>'Sure',
'Your_Activaiton_Code'=>'Your Activaiton Code',
'Thanks'=>'Thanks',
'Hoor_Activation_Code'=>'Hoor Activation Code',
'Resend_Code_Successfully'=>'Resend Code Successfully',
'Active_Code_Successfully'=>'Active Code Successfully',
'Wrong_Active_Code'=>'Wrong Activation Code',
'Salon_or_Beauty_Expert'=>'Salon or Makeup Artist',
'Salon'=>'Salon',
'Forget_Password'=>'Forget Password',
'Beauty_Expert'=>'Makeup Artist',
'Phone_Number_or_Email'=>'Phone Number or Email',
'incorrect_information_site_login'=>'The email, phone number, or password is incorrect, or you may not have activated the account through the activation code sent to the email',
'Logout'=>'Logout',
'Reset_Password'=>'Reset Password',
'New_Password'=>'Create New Password',
'Click_Here_to_create_new_password'=>'Click Here to Create New Password',
'OR'=>'Or',
'We_will_email_you_the_instructions'=>'We will email you the instructions',
'Copy_this_link'=>'Copy This Link',
'Continue'=>'Continue',
'Just_Enter_YourEmail_or_Phone_Number'=>'Just Enter Your Email or Phone Number',
'WrongEmailOrPhoneNumber'=>'There is no Email or Phone Number like this',
'New_Password_Successfully'=>'New Password Created Successfully',
'Confirm_Password_Not_Same'=>'Confirm Password is not Same Password',
'Confirm_Password'=>'Confirm Password',
'Should_to_Approve_First'=>'Should to Approve First to Coontinue Your Data',
'Commercial_Record_or_Self_employment_document'=>'Commercial Record / Self Employment Document',
'Continue_Ur_Data'=>'You must complete your data from here in order to use the website services',
'Approve_From_Admins'=>'You are now inactive, you must wait for it to be activated by the supervisors and then complete your data',
'Salons'=>'Salons',
'Makeup_Artist'=>'Makeup Artist',
'Join_Rquests'=>'Join Rquests',
'Download'=>'Download',
'Salons_Requests'=>'Salon  Join Rquests',
'Makeup_Requests'=>'Makeup Artists Join  Rquests',
'Not_Verifed'=>'Did not Create an Activation Code',
'Approve'=>'Approve',
'Approved'=>'Approved',
'Should_to_Complete_Data_First'=>'Should to Complete your Data First',
'Profile'=>'Profile',
'Change_Image'=>'Change Image',
'Change_Password'=>'Change Password',
'Old_Password'=>'Old Password',
'New_Password'=>'New Password',
'Confirm_New_Password'=>'Confirm New Password',
'Save_Changes'=>'Save Changes',
'DASHBOARD'=>'Dashboard',
'ConfirmPassword_NotSame'=>'Confirm Password is not Same New Password ',
'OldPassword_Incorrect'=>'Old Password Incorrect',
'No_New_Password'=>'You Sholud Enter New Password',
'Our_Info'=>'My Information',
'Our_Data'=>'My Data',
'Continue_Data'=>'Continue My Data',
'Download_Commercial_Record_or_Self_employment_document'=>'Download Commercial Record / Self Employment Document',
'Arabic_Title_SalonArtist'=>'Arabic Salon/Expert Name',
'English_Title_SalonArtist'=>'English Salon/Expert Name',
'Arabic_Desc_SalonArtist'=>'About Salon/Expert in Arabic',
'English_Desc_SalonArtist'=>'About Salon/Expert in English',
'Work_Time'=>'Work Time',
'Add_My_Data'=>'Add My Data',
'Arabic_TitleSMRequired'=>'Arabic Salon/Expert Name is Required',
'English_TitleSMRequired'=>'English Salon/Expert Name is Required',
'Arabic_DescSMRequired'=>'About Salon/Expert in Arabic is Required',
'English_DescSMRequired'=>'About Salon/Expert in English is Required',
'LocationRequired'=>'Location is Required',
'AddressRequired'=>'Address is Required',
'TimeWorkRequired'=>'Time Work is Required',
'ImageRequired'=>'Image is Required',
'Upadated_Data'=>'Upadated Data Successfully',
'Complete_Your_Data_Successfully'=>'Your Data is Completed Successfully',
'Perivious_Works'=>'Perivious Works',
'Add_Works'=>'Add Works',
'Offers'=>'Offers',
'Offer_Service'=>'Services at Offer',
'Start_Date_Offer'=>'Offer Start Date',
'End_Date_Offer'=>'Offer End Date',
'Price_Offer'=>'Offer Price',
'Image_Offer'=>'Offer Image',
'Offer_Arabic_Name'=>'Offer Arabic Name',
'Offer_English_Name'=>'Offer English Name',
'Offer_Start_Date'=>'Offer Start Date',
'Offer_End_Date'=>'Offer End Date',
'Offer_Arabic_Service'=>'Services at Offer in Arabic',
'Offer_English_Service'=>'Services at Offer in English',
'Offer_Price'=>'Offer Price',
'Offer_Image'=>'Offer Image',
'Offer_Arabic_NameRequired'=>'Offer Arabic Name is Required',
'Offer_English_NameRequired'=>'Offer English Name is Required',
'Arabic_ServiceRequired'=>'Services at Offer in Arabic is Required',
'English_ServiceRequired'=>'Services at Offer in English is Required',
'Start_DateRequired'=>'Offer Start Date is Required',
'End_DateRequired'=>'Offer End Date is Required',
'PriceRequired'=>'Offer Price is Required',
'PriceNumeric'=>'Offer Price Should be A Number',
'ImageRequired'=>'Offer Image is Required',
'Imageimage'=>'Image Should be an Image no File',
'Start_Dateafter'=>'Offer Start Date Should not be on a Previous Date',
'End_Dateafter'=>'Offer End Date Should Be After Start Date',
'Waiting_Offer'=>'Waiting Offers to Accepted',
'Accepted_Offer'=>'Accepted Offers',
'Add_Offer'=>'Add Offer',
'Post_Offer'=>'Post Offer',
'Delete_Offer'=>'Delete Offer',
'Are_u_Sure_Want_Delete_Offer'=>'Are You Sure Want Delete This Offer',
'Are_u_Sure_Want_Delete_Expert'=>'Are You Sure Want Delete This Beauty Expert',
'Cancel'=>'Cancel',
'Yes'=>'Yes',
'Halll'=>'Hall',
'Takeaway'=>'Takeaway',
'Edit_Offer'=>'Edit Offer',
'Salon_or_Expert'=>'Salon / Expert',
'ExpertSalon'=>'Makeup Artists of Salon',
'Contact_With_Hoor'=>'Contact With Hoor',
'Will_Communcate_With_U_Soon'=>'Thank You We Will Communcate With You Soon',
'Sure_From_Date'=>'Please check the date of the start and end of the show well because you cannot amend it later',
'Expert_Arabic_Name'=>'Beauty Expert Arabic Name',
'Expert_English_Name'=>'Beauty Expert English Name',
'Expert_Image'=>'Beauty Expert Image',
'Add_Expert'=>'Add Beauty Expert',
'Edit_Expert'=>'Edit Beauty Expert',
'Expert_Arabic_NameRequired'=>'Beauty Expert Arabic Name is Required',
'Expert_English_NameRequired'=>'Beauty Expert English Name is Required',
'SalonTimes'=>'Times',
'SalonServices'=>'Services',
'Days'=>'Work Days',
'Timess'=>'Work Times',
'From_Time'=>'From Time',
'To_Time'=>'To Time',
'From_TimeRequired'=>'From Time is Required',
'To_TimeRequired'=>'To Time is Required',
'Should_Choice_Day'=>'Should to Choice at Least one Day',
'Sat'=>'Saturday',
'Sun'=>'Sunday',
'Mon'=>'Monday',
'Tue'=>'Tuesday',
'Wed'=>'Wednesday',
'Thr'=>'Thursday',
'Fri'=>'Friday',
'Service_Category'=>'Service Category',
'Service_Arabic_Name'=>'Service Arabic Name',
'Service_English_Name'=>'Service English Name',
'Service_Price'=>'Service Price',
'Service_Duration'=>'Service Duration',
'Add_Service'=>'Add Service',
'Half'=>'Half Hour',
'One_Hour'=>'One Hour',
'One_Hour_Half'=>'One Hour and Half',
'Two_Hour'=>'Two Hour',
'Two_Hour_Half'=>'Two Hour and Half',
'Three_Hour'=>'Three Hour',
'Are_u_Sure_Want_Delete_Service'=>'Are You Sure Want Delete This Service',
'Edit_Service'=>'Edit Service',
'Service_Arabic_NameRequired'=>'Service Arabic Name is Required',
'Service_English_NameRequired'=>'Service English Name is Required',
'Service_PriceRequired'=>'Service Price is Required',
'Service_CategoryRequired'=>'Service Category is Required',
'Service_DurationRequired'=>'Service Duration is Required',
'Service_PriceNumeric'=>'Service Price Should be a Number',
'Offer_Requests'=>'Offer Requests',
'Salon_Offers'=>'Salon Offers',
'Artist_Offers'=>'Artist Offers',
'Accepted_Offer'=>'Offer Accepted',
'Offer_Name'=>'Offer Name',
'Start_Date'=>'Start Date',
'End_Date'=>'End Date',
'Explain_Service'=>'Explain Service',
'AcceptedOffer'=>'Accept Offer',
'RejectedOffer'=>'Reject Offer',
'Rejected_Offer'=>'Offer Rejected',
'Offers_Waiting'=>'Waiting Offers',
'Artist'=>'Makeup Artist',
'SALONS'=>'Salons',
'EXPERTS'=>'Beauty Experts',
'LOOKING_FOR_SERVICES'=>'Looking For Services',
'Blocked'=>'Blocked',
'Salon_Name'=>'Salon Name',
'Salon_Desc'=>'Salon Description',
'Time'=>'Work Time',
'Info'=>'Information',
'Previous_Works'=>'Previous Works',
'TIMES'=>'Work Times',
'Beauty_Artists'=>'Makeup Artists of Salon',
'Block'=>'Block',
'DisBlock'=>'Dis Block',
'Salon_Blocked'=>'Salon Blocked',
'Active_Salon'=>'Salon Active',
'Alert_User_Delete_Salon_Expert'=>'Are You Sure Want Delete This Salon/Beauty Expert Knowing that if deletion, all of this offers, services, log-in, and everything will be deleted',
'Service_Name'=>'Service Name',
'Duration'=>'Duration',
'Service_Category'=>'Service Category',
'Works_Days'=>'Work Days',
'Now_Blocked'=>'Blocked Now',
'Active_Now'=>'Active Now',
'Artist_Blocked'=>'Makeup Artist Blocked',
'Active_Artist'=>'Makeup Artist Active',
'Artist_Desc'=>'About Makeup Artist',
'Explain_Service'=>'Explain Service',
'Transfer_To_Choice_Service'=>'Continue To Choice Service',
'Choice_Gifts'=>'Choice Gift',
'SR'=>'SR',
'Send_Gift'=>'Send Gift',
'Send_NameRequired'=>'Name_Of_Recipient is Required',
'Send_PhoneRequired'=>'Phone Of Recipient is Required',
'Send_DateRequired'=>'Date of Send is Required',
'Send_EmailRequired'=>'Email Of Recipient is Required',
'Please_Pay_To_Continue'=>'Please complete the process by paying the amount to confirm sending the gift',
'Name_Of_Recipient'=>'Name Of Recipient',
'Phone_Of_Recipient'=>'Phone Of Recipient',
'Date_of_Send'=>'Date of Send',
'Email_Of_Recipient'=>'Email Of Recipient',
'Your_Message'=>'Your Message',
'Send'=>'Send',
'Pay_Gift'=>'Pay Gift',
'Client_Srvice'=>'Client Srvice',
'The_payment_process_is_fully_insured'=>'The Payment Process is fully Insured',
'Complete_Operation'=>'Complete Operation',
'Thanks'=>'Thanks',
'Thanks_For_Use_Website_Hoor'=>'Thanks For Use Hoor Website',
'Back'=>'Back',
'Connected'=>'Connected',
'Send_Datedate'=>'Date of Send Should be a Valid Date',
'Send_Dateafter'=>'Date of Send Should be a Newest Date not Oldest Date',
'Not_Payed'=>'Not Completed (Not Pay)',
'Looking_Name'=>'Sender Name',
'Looking_Phone'=>'Sender Phone',
'Looking_Email'=>'Sender Email',
'Gift_Value'=>'Gift Value',
'Gift_Code'=>'Gift Code',
'Hoor_Gifts_Requests'=>'Hoor Gifts Requests',
'Connect'=>'Connect',
'DisConnect'=>'Dis Connect',
'DisConnected'=>'Dis Connected',
'Send_Gift_By_Email'=>'Send Gift By Email',
'Email_Send_Successfully'=>'Email Send Successfully',
'Your_Have_Gift_From'=>'You Have Gift From',
'Gift_is_Coupon_Discount_Code_With_Value'=>'Gift is Discount Code With Value',
'Gift_Code_is'=>'Gift Code is',
'Send_To'=>'Send To',
'Time_To_Send'=>'Date To Send Gift',
'Offers_End'=>'Offers End',
'Rate'=>'Rates',
'Reserved_Now'=>'Reserved Now',
'Works'=>'Works',
'Information'=>'Information',
'Employees'=>'Employees',
'Categories'=>'Categories',
'Add_Category'=>'Add Category',
'Account_Block'=>'This Account is Block Now Connect To Admins To Know Reason From Here',
'Now_You_Are_Blocked'=>'Now You Are Blocked You Can Not Browse any Page on Website',
'You_Already_Choiced'=>'You Already Choiced This Category',
'Offer_Present_From_Salon'=>'This Offer is From Salon',
'Offer_Present_From_Expert'=>'This Offer is From Beauty Expert',
'Offer_End_at'=>'This Offer is Valid Until',
'Choice_Offer'=>'Choice Offer',
'Discover_the_best_salons_near_you'=>'Discover The Best Salons Near You',
'More'=>'More',
'Discover_Best_Offers'=>'Discover Best Offers',
'End_Offer'=>'Offers Ended',
'More_Than'=>'More than 1000 makeup artists, therapists, salon experts and spa at
 More than 60 countries use the appointment booking program',
'CommentRequired'=>'Comment is Required',
'Comments'=>'Comments',
'Comment'=>'Comment',
'Rate_Here'=>'Rate Here',
'Rateee'=>'Rate',
'rateRequired'=>'You Should Choice Star at Least',
'Report'=>'Report',
'Rates'=>'Rates',
'Reportd_Comment'=>'Reportd Comment',
'Cancel_Report'=>'Cancel Report',
'Reported'=>'Reported',
'Canceled'=>'Canceled',
'Preview'=>'Preview Profile',
'Back_Dashboard'=>'Back to Dashboard',
'Rate_Total'=>'Total Rate',
'User'=>'User',
'Reports_Comments'=>'Reports Comments',
'Choiced_Services'=>'Choiced Services',
'Reservation'=>'Reservation',
'Should_To_Choice_Service_First'=>'Should to Choice Service First',
'Service_Provider'=>'Service Provider',
'Alert_Reserve_Experts_Choice'=>'When selecting a specific service provider, an amount is increased by',
'Alert_Reserve_Experts'=>'It may be that this service provider does not provide all the services chosen, so it must be noted', 'Date_Time'=>'Date/Time',
'Work_Times'=>'Work Times',
'Date'=>'Date',
'Timeeeee'=>'Time',
'SubTotal'=>'Sub Total',
'Total_Services'=>'Total Services',
'Total'=>'Total',
'ServTotalHours'=>'Service Total Hours',
'Hours'=>'Hours',
'Choice_Payment_Method'=>'Choice Payment Method',
'ExpertRequired'=>'Should to Choice Expert',
'Cant_Take_All_Services_In_This_Time'=>'You cannot book because the number of hours of selected services exceeds the official working hours of the salon, and you can reduce the services until the reservation is made',
'Please_Pay_Till_Sure_Ur_Reservation'=>'Please pay to confirm your reservation',
'Pay_and_Sure_Reserve'=>'Payment and reservation confirmation',
'Bill'=>'Bill',
'Details'=>'Details',
'Reserv_Date'=>'Reservation  Date',
'Reserv_Time'=>'Reservation  Time',
'Custom_Bill'=>'Custom Bill',
'Retrieval_Policy_in_ArabicRequired'=>'Retrieval Policy in Arabic Required',
'Retrieval_Policy_in_EnglishRequired'=>'Retrieval Policy in English Required',
'Retrieval_Policy_in_Arabic'=>'Retrieval Policy in Arabic',
'Retrieval_Policy_in_English'=>'Retrieval Policy in English',
'Terms_in_Arabic'=>'Terms and Conditions in Arabic',
'Terms_in_English'=>'Terms and Condition in English',
'Retrieval_Policy'=>'Retrieval Policy',
'Selected_Choice'=>'Selected Choice',
'Random_Choice'=>'Random Choice',
'Will_More'=>'The amount will be increased due to your specific choice and amount',
'Increase_Price'=>'Increase Price at the Specified Choice',
'IncreasePriceRequired'=>'Increase Price at the Specified Choice is Required',
'Selected_Choice'=>'Selected Choice',
'NO'=>'No',
'YES'=>'Yes',
'Sorry_All_Expert_Busy_In_That_Date'=>'Sorry, all experts are busy on this date. It is possible to try another date',
'Sorry_This_Time_Reserved_With_This_Expert'=>'Sorry This Time is Reserved With This Expert',
'Sorry_Time_Of_Selected_Services_More'=>'We apologize for the number of hours of service selected, in addition to the official time to make the salon',
'Coupon'=>'Discount Coupon',
'Total_Before_Disc'=>'Total Before Discount',
'Get'=>'Get',
'Cancel'=>'Cancel',
'Will_Disc'=>'An amount will be deducted from the final bill and its amount',
'Success_Code'=>'Success Coupon Code',
'Wrong_Code'=>'Wrong Coupon Code',
'Current_Bookings'=>'Current Bookings',
'Contemporary_Reservations'=>'Contemporary Reservations',
'Reserv_Not_Payed'=>'Reservations Not Payed',
'Timer'=>'Time',
'Bill_Num'=>'Bill Number',
'Expertt'=>'Expert',
'Name_of_Client'=>'Name of Client',
'Phone_of_Client'=>'Phone of Client',
'Random_Expert'=>'Random Expert',
'Date_Resrv'=>'Resrvation Date',
'ReportReservation'=>'Report Reservation',
'Reported'=>'Reported',
'Amount'=>'Amount',
'Date_Bill'=>'Date Bill',
'From_Date'=>'From Date',
'To_Date'=>'To Date',
'You_Not_Choiced_Any_Thing'=>'You Not Choiced Any Thing',
'No_Data_Like_That'=>'Can not Find Bill Like That',
'Reports_Resrevation'=>'Resrevations Reports',
'Canceled'=>'Canceled',
'Resrevations'=>'Resrevations',
'Reported_and_NotPay'=>'Reported and Not Payed',
'Reportedd'=>'Reported',
'End_Resrev'=>'Resrevation Ended',
'Artist'=>'Makeup Artist',
'Gift_Value'=>'Gift Value',
'Expert_NumRequired'=>'Expert Numbers is Required',
'Expert_Num'=>'Expert Numbers',
'Expert_Numnumeric'=>'Expert Numbers Should be a Number',
'Sorry_Maximum_Random'=>'Sorry, the maximum limit for random reservations has been reached on this date. You can try another date or choose a specific expert to make the reservation',
'Offer'=>'Offer',
'Offer_Start_End'=>'Offer Start Date - Offer End Date',
'Offer_Services'=>'Offer Services',
'Sorry_This_Time_Reserved'=>'Sorry This Time Reserved',
'Offers_Reservations'=>'Offers Reservations',
'Offer_Bills'=>'Offer Bills',
'End_Reserv'=>'Ended Reservation',
'End_Reserv_and_Not_Payed'=>'Ended Reservation and Not Payed',
'Salon_Offers'=>'Salon Offers',
'Offer_Date'=>'Offer Date',
'Expert_Offers'=>'Expert Offers',
'Search'=>'Search',
'Filter'=>'Filter',
'Results'=>'Results',
'Work_Days'=>'Work Days',
'Search_Salon_Name'=>'Write Salon Name Here',
'Search_Artist_Name'=>'Write Expert Name Here',
'Favorites'=>'Favorites',
'UnFave'=>'Un Favoratie',
'Current_Balance'=>'Current Balance',
'Credit_Balance'=>'Refunds for the client portfolio',
'Email_or_Phone_User'=>'Email or Phone Number of Client',
'Recovery'=>'Recovery',
'Recover_Amount'=>'Recover Amount',
'EmailPhoneRequired'=>'Email or Phone Number of Client is Required',
'RecoverAmountRequired'=>'Recover Amount is Required',
'RecoverAmountnumeric'=>'Recover Amount Should be a Number',
'No_Client_Like_That'=>'No Client Like That',
'Recovered'=>'Recovered Successfully',
'Recoverd_Balance'=>'Recoverd Balance',
'Recoverd_Again'=>'Recoverd Again',
'RUSWRAg'=>'Are You Sure Want Recover This Amount Again',
'Alert_Zero_Balance'=>'You cannot get the amount back because this customers balance is not enough',
'My_Resrvation'=>'My Resrvation',
'Credit_New_Balance'=>'Credit New Balance to Wallet',
'Current'=>'Current',
'Ended'=>'Ended',
'ProductsWithStart_Periods'=>'Products With Start Periods',
'Reservations'=>'Reservations',
'Credit_Amount'=>'Credit Amount',
'Pay_Credit'=>'Pay Credit',
'Please_Pay_To_Continue_Wallet'=>'Please make the payment to have the balance transferred to your wallet',
'Arabic_Currency'=>'Arabic Currency Name',
'English_Currency'=>'English Currency Name',
'Used_Num'=>'The Number of Times of Use',
'SortBackAmount'=>'Sort of The amount returned to the portfolio',
'BackAmount'=>'The amount returned to the portfolio',
'Rte'=>'Static Rate',
'num'=>'Static Number',
'Code_Type'=>'Code Type',
'Normal'=>'Normal',
'Gift'=>'Gift',
'Credit_Done'=>'The Balance was Charged to your wallet with a value',
'Hoor_Credit_Wallet'=>'Hoor Credit Wallet',
'Wallet_Credit_Bill'=>'Wallet Recharge Bills',
'Should_Login_To_Continue'=>'You must be logged in to continue choosing the gift',
'Offer_Time_TwoH'=>'When the offer is added, the service time of the offer is two hours',
'Offer_Bill_Delete'=>'Knowing if there are invoices for this offer, they will also be deleted',
'Del_Code_Bill'=>'Note that if there are invoices for this code, it will not be deleted, and if it is deleted, all invoices for this code will be deleted',
'Gift_Value_More_Total'=>'You cannot use this code at this time because the discount amount is greater than the total amount', 'Add_Fave'=>'Add To Favorite',
'Del_Fave'=>'Delete From Favorite',
'Added_Successfully'=>'Added Successfully',
'Deleted_Fave'=>'Deleted From Favorite',
'Premations'=>'Premations',
'Add_User'=>'Add User',
'Salon_Blocked'=>'Salon Blocked',
'Are_u_Sure_Want_Delete'=>'Are You Sure Want Delete',
'Choice_Language'=>'Choice Language',
'Arabic'=>'Arabic',
'English'=>'English',
'Language'=>'Language',
'Notifications'=>'Notifications',
'Read'=>'Read',
'UnRead'=>'Un Read',
'AUSWDA'=>'Are You Sure Want Delete All Notifications',
'Salons_Num'=>'Salons Numbers',
'Experts_Num'=>'Makeup Artists Numbers',
'Salon_Offers_Num'=>'Salon Offers Numbers',
'Artist_Offers_Num'=>'Makeup Artist Offers Numbers',
'Resrvation_Num'=>'Resrvation Numbers',
'Messages_Num'=>'Messages Numbers',
'Messages_SA_Num'=>'Messages Salon and  Makeup Artists Numbers',
'Looking_Num'=>'Lookings of Services Numbers',
'Request_Gift_Num'=>'Request Gifts Numbers',
'Resrvation_Ended_Num'=>'Resrvation Ended Numbers',
'Resrvation_Reports_Num'=>'Resrvation Reports Numbers',
'Join_Requests'=>'Join Requests',
'Offer_Requests'=>'Offer Requests',
'Reservations_Not_Payed'=>'Reservations Not Payed',
'Comments_Reports'=>'Comments Reports Numbers',
'Link'=>'Link',
'EnterEmail'=>'Enter Email',
'EnterPassword'=>'Enter Password',
'Login_Nw_To_Can_Reserved'=>'Login Now To Can Reserved',
'Accepted_On'=>'Accepted On',
'And'=>'And',
'More_Cat_Contact_Hoor'=>'If you want to add sections that are not available, you can contact Hoor to add them',
'Rights'=>'Rights',
'RightsRequired'=>'Rights is Required',
'Alert_Looking_Bill'=>'Note that it is possible that this service seeker is linked to reservations and bills will also be deleted if the deletion is done',
'Alert_Del_Resrev'=>'Note that if this reservation is deleted, its invoice will be deleted',
'Cancel_Report'=>'Cancel Report',
'General'=>'General',
'Expire_Date'=>'Expire Date',
'Attach_Message'=>'Attach Message',
'This_Code_Expired'=>'This Code Expired',
'Expired_Code'=>'Expired Code',
'Contact_SA'=>'Contact With Salons and Experts',
'Send_Message'=>'Send Message',
'Send_Successfully'=>'Send Successfully',
'Send_Message_To_All'=>'Send Message To All',
'Artist_Name'=>'Expert Name',
'Change_Price'=>'Change Price',
'Arabic_Explain_Cat'=>'Arabic Explain Category',
'English_Explain_Cat'=>'English Explain Category',
'Explain_Cat'=>'Explain Category',
'No_Explain'=>'No Explain',
'Reply'=>'Reply',
'Replies'=>'Replies',
'Offers_Reservations_Current'=>'Current Offers Reservations',
'Offers_Reservations_Ended'=>'Ended Offers Reservations',
'Reserv_Days'=>'Reservations Days',
'Resrv_Days'=>'Reservations Days',
'Reserva_Days'=>'Number of Booking Days',
'Offer_Resrv_Days'=>'Number of Offer Booking Days',
'All_Right_Reserved'=>'All Right Reserved',
'Resrev_Bill_Num'=>'Resrevation Bill Number',
'Sort_Bill'=>'Bill Sort',
'Salon_Reserva'=>'Salon Bill',
'Salon_Offer_Reserva'=>'Salon Offer Bill',
'Artist_Reserva'=>'Makeup Artist Bill',
'Artist_Offer_Reserva'=>'Makeup Artist Offer Bill',
'Credit_Wallet'=>'Credit Wallet Bill',
'Gift'=>'Gift Bill',
'Client_Details'=>'Client Details',
'Client_Bill'=>'Client Bill',
'No_Bill_Like_That'=>'No Bill Like That',
'Admin'=>'Admin',
'Canceld_Reported'=>'Canceld Report',
'Done'=>'Done',
'Implementation'=>'Implementation',
'Rateee'=>'Rate',
'Salon_Bill'=>'Salon Bill',
'Artist_Bill'=>'Artist Bill',
'Hoor_Bill'=>'Hoor Bill',
'No_Bill'=>'No Bill',
'New_Total'=>'New Total',
'Department_Change'=>'Department Change',
'Bill_Num_Reserv'=>'Bill Number of Reservation',
'Should_20_Min'=>'At least 20 minutes must pass for the report to be accepted',
'Discount_Rate'=>'Discount Rate',
'Active'=>'Active',
'Biils'=>'Bills',
'Salons_Offer'=>'Salons Offers',
'Report_Bills'=>'Report Bills',
'Report_Offer_Bills'=>'Report Offer Bills',
'Print'=>'Print',
'Report_Time'=>'Report Time',
'Word_ArabicRequired'=>'Caption in Arabic Required',
'Word_EnglishRequired'=>'Caption in English Required',
'Word_Arabic'=>'Caption in Arabic',
'Word_English'=>'Caption in English',
'Active_Account'=>'Active Account',
'Payment_Method'=>'Payment Method',
'Failed_Pay'=>'Failed Pay',
'Checkout'=>'Checkout',
'Pay'=>'Pay',
'Wallet_Balance'=>'Wallet Balance',
'Wallet_Pay'=>'Pay by Wallet',
'This_Email_Already_Actived'=>'This Email is Already Actived',
'Not_Enough_Money_in_Wallet'=>'There is not enough balance in the wallet',
'Will_Blocked'=>'You will be banned if the site’s security system is tampered with',
'Code_Not_Send'=>'Code Not Send',
'Alert_Code'=>'This proxy is better to use if the activation code does not send after it is long before it is necessary to integrate. Are you destroyed by the implementation of this procedure to be communicating with you by supervisors',
'Alert_Reg'=>'Alert Registrations',
'Connected'=>'Connected',
'Artists_Offer'=>'Artists Offers',
'Age'=>'Age',
'Resrvations'=>'Resrvations',
'Congrats_Your_Resrvations_is_Confirmed'=>'Congrats Your Resrvations is Confirmed',
'Back'=>'Back',
'Hoor_Type'=>'Hoor Types',
'R_U_Salon_or_Artist'=>'Are you a service provider (Salon / Beauty Expert) ?',
'Online'=>'Online',
'Upon_Arrival'=>'Upon Arrival',
'Both'=>'Both',
'Offers_Num'=>'Offers Numbers',
'Hoor_Paymnet'=>'Hoor Paymnet',
'Hoor_Addition'=>'Hoor Additions',
'Deposit'=>'Deposit',
'Residual'=>'Residual',
'Consists'=>'Consists',
'Store_Qty'=>'Store Qty',
'ManufacturingOrderSechdule'=>'Manufacturing Order Sechdule',
'Total_Required_Qty'=>'Total Required Qty',
'Required_Qty'=>'Required Qty',
'Name_Outcome'=>'Name Outcome',
'Except_Qty'=>'Except Qty',
'ManufacturingOrder'=>'Manufacturing Order',
'ManufacturingModelPrecent'=>'Manufacturing Model Precent',
'Total_New_Qty'=>'Total New Qty',
'NewQty'=>'New Qty',
'Manufacturing_Model'=>'Manufacturing Model',
'Refused'=>'Refused',
'Quality_Qty_Purch_Order'=>'Quality and Qty in Purchases Order',
'Use'=>'Use',
'Purch_Date'=>'Purchaces Date',
'Purch_Reason'=>'Purchaces Reason',
'cost_price_purch'=>'Cost Price Purchases',
'cost_price_sales'=>'Cost Price Sales',
'Print_Text_Footer_Quote'=>'Print Text Footer Quote',
'Manufacturing_Model_Shortcomings'=>'Manufacturing Model in Shortcomings',
'Not_Use'=>'Not Use',
'Secretariat'=>'Secretariat',
'Letter_Bill'=>'Letter Bill',
'Store_Code'=>'Store Code',
'GroupsSales'=>'Groups Sales',
'Patch_Number'=>'Patch Number',
'Account_Ehlak'=>'Account Destruction',
'VendorPricesReport'=>'Vendor Prices Report',
'DailyMoves'=>'Daily Moves',
'StoresTarnsferPrice'=>'Change Price in Stores Transfer ?',
'StoresQty'=>'Stores Qties',
'Settlements'=>'Settlements',
'NumbersOfBill'=>'Numbers Of Bill',
'Shortcomings'=>'Shortcomings',
'Bill_Numbers'=>'Bill Numbers',
'Later_Due'=>'Later Due',
'Manufacturing_Request_Sechdule'=>'Manufacturing Request Sechdule',
'Delegate_Phone'=>'Delegate Phone',
'Recived_Date'=>'Recived Date',
'Client_Phone'=>'Client Phone',
'Client_Address'=>'Client Address',
'Manufacturing_Request'=>'Manufacturing Request',
'Sale_Date'=>'Sale Date',
'Approved'=>'Approved',
'Data'=>'Data',
'QuoteImagesSechdules'=>'Quote Images Sechdules',
'QuoteImages'=>'Quote Images',
'Bill_Nums'=>'Bill Numbers',
'Total_Bill'=>'Total Bill',
'Total_Cash_Bill'=>'Total Cash Bill',
'Total_Later_Bill'=>'Total Later Bill',
'Total_Later_Bill_Collected'=>'Total Later Bill Collected',
'Sales_Delegates'=>'Sales Delegates',
'All_Products'=>'All Products',
'Collection_Delegates'=>'Delegates Collection',
'Exmine_Type'=>'Exmine Type',
'Result'=>'Result',
'Accept'=>'Accept',
'Client_Delegate'=>'Show Just Client of Delegate Responsible',
'StoresInventory'=>'Stores Inventory',
'Quality'=>'Quality',
'RUSWDTQualityDOne'=>'Are You Sure Want to Quality Done ?',
'QualitySechdule'=>'Quality Sechdule',
'Execution_Code'=>'Execution Code',
'Allow_From'=>'Allow From',
'ExecutionSave'=>'Save & Execution',
'Cash_Collection'=>'Cash & Collection',
'Allow_To'=>'Allow To',
'ExaminationsTypes'=>'Examinations Types',
'EditManufacturingExecution'=>'Edit Manufacturing Execution',
'ManufacturingExecutionSechdule'=>'Manufacturing Execution Sechdule',
'Manu_Order_Code'=>'Manufacturing Order Code',
'Manu_Order_Date'=>'Manufacturing Order Date',
'Quality_Manager'=>'Quality Manager',
'Outcome_Name'=>'Outcome Name',
'Outcome_Code'=>'Outcome Code',
'Outcome_Qty'=>'Outcome Qty',
'Outcome_Store'=>'Outcome Store',
'Outcome_Unit'=>'Outcome Unit',
'RequiredQty'=>'Required Qty',
'ManufacturingExecution'=>'Manufacturing Execution',
'Recipient'=>'Recipient',
'ExchangeManufacturingGoodsSechdule'=>'Exchange Manufacturing Goods Sechdule',
'TransferManufacturingOrder'=>'Transfer to Manufacturing Order',
'Done'=>'Done Successfully',
'RUSWExchangeManfactureGoods'=>'Are You Sure Want Exchange Manfacture Goods',
'RUSWSurExchangeManfactureGoods'=>'Are You Sure Want to Sure Exchange Manfacture Goods',
'YesandPrint'=>'Yes and Print',
'Manufacture_Request_Code'=>'Manufacture Request Code',
'For_Client'=>'For Client',
'EditManufacturing_Request'=>'Edit Manufacturing Request',
'Finshed'=>'Finshed',
'RUSWTApprove'=>'Are You Sure Want Approve',
'RUSWTEnd'=>'Are You Sure Want End',
'Production_Manager'=>'Production Manager',
'ShortcomingsSechdule'=>'Shortcomings Sechdule',
'Later_Sales_Bill'=>'Later Sales Bill',
'Account_Client_Code'=>'Account Client Code',
'Name_Sales_Bill'=>'Name Sales Bill',
'Name_Quote_Bill'=>'Name Quote Bill',
'manu_order_precent'=>'Manufacturing Order Precent',
'Total_Precent_Residual'=>'Total Precent Residual',
'Name_Sales_Order_Bill'=>'Name Sales Order Bill',
'Secretariat_Stores_Qty'=>'Secretariat Stores Qties',
'Edit_Secretariat_Export_goods'=>'Edit Secretariat Export goods',
'Secretariat_Export_goods_Sechdule'=>'Secretariat Export goods Sechdule',
'Edit_Secretariat_Import_goods'=>'Edit Secretariat Import goods',
'Secretariat_Import_goods'=>'Secretariat Import goods',
'Secretariat_Export_goods'=>'Secretariat Export goods',
'Secretariat_Import_goods_Sechdule'=>'Secretariat Import goods Sechdule',
'Secretariat_Stores'=>'Secretariat Stores',
'Edit_Sales_Bill'=>'Edit Sales Bill',
'Edit_Purchases_Bill'=>'Edit Purchases Bill',
'Safes_Balances'=>'Safes Balances',
'pos_hold'=>'Hold Bill in POS ?',
'Group_Brand'=>'Group Brand in Search',
'Reported_Client'=>'Reported Client',
'Refused_Client'=>'Refused by Client',
'Editedd'=>'Edited',
'Sales_Bill_Nums'=>'Sales Bill Numbers',
'Return_Sales_Bill_Nums'=>'Return Sales Bill Numbers',
'Maintaince_Bill_Nums'=>'Maintaince Bill Numbers',
'Return_Maintaince_Bill_Nums'=>'Return Maintaince Bill Numbers',
'EmployeeCommissionDiscounts'=>'Employee Commission Discounts',
'pos_stores'=>'POS Stores ?',
'Start_Periods'=>'Start Periods',
'Products_Start_Periods'=>'Products Start Periods',
'Empp'=>'Do You Want Employee Account in Account ?',
'Highest_Price'=>'Highest Price',
'Lowest_Price'=>'Lowest Price',
'Price_List'=>'Price List',
'Must_Sure_Avqty'=>'Must Sure Avaliable qty for all products',
'MaintainceColors'=>'Maintaince Colors',
'ConsistsSechdule'=>'Consists Sechdule',
'Consist'=>'Consist',
'Returnn_Exchange'=>'Returnn and Exchange',
'NoteRecived'=>'Recived Note',
'Safe_Value'=>'Safe Value',
'Seal'=>'Seal',
'Maintaince'=>'Maintaince',
'Sure_Bill'=>'Sure Bill',
'Auto'=>'Auto',
'By_Admin'=>'By Admin',
'Barcode_Print'=>'Barcode Print',
'Unit_Print'=>'Unit Print',
'Total_BF_Print'=>'Total Before Print',
'Discount_Print'=>'Discount Print',
'Tax_Print'=>'Tax Print',
'RefuseReason'=>'Refuse Reason',
'Consists'=>'Consists',
'RefuseReasons'=>'Refuse Reasons',
'Loan_Installment'=>'Loan Installment',
'Quick_Shortcut'=>'Quick Shortcut',
'ExpensesReport'=>'Expenses Report',
'DailyProducts'=>'Daily Products',
'Vendor_Date'=>'Vendor Date',
'Delegate_Sale'=>'Delegate Sale',
'Delegate_Purchase'=>'Delegate Purchase',
'Note'=>'Note',
'Hide'=>'Hide',
'Show_Hide_Data'=>'Show Hide Data',
'Executor_Sales'=>'Executor Sales',
'cost_price'=>'Cost Price in Stores Transfer',
'Wallet'=>'Wallet',
'Appear'=>'Appear',
'Hidden'=>'Hidden',
'Shifts_Numbers'=>'Shifts Numbers',
'DailyShifts'=>'Daily Shifts',
'Date_Open_Shifts'=>'Date Open Shifts',
'Shift_Num'=>'Shift Number',
'Total_Recipt'=>'Total Recipt Voucher',
'Total_Payment'=>'Total Payment Voucher',
'Discount_Codes'=>'Discount Codes',
'Hoor_Gifts_Codes'=>'Hoor Gifts Codes',
'Pay_Upon_Arrival'=>'Pay Upon Arrival',
'Reasch_to_Limit'=>'You have reached the limit for adding offers',
'Have_Num_of_Offers'=>'You have a limited number of offers and they are',
'Gift_Value_1Required'=>'Gift Value 1 is Required',
'Gift_Value_1Numeric'=>'Gift Value 1 Should be a Number',
'Gift_Value_2Required'=>'Gift Value 2 is Required',
'Gift_Value_2Numeric'=>'Gift Value 2 Should be a Number',
'Gift_Value_3Required'=>'Gift Value 3 is Required',
'Gift_Value_3Numeric'=>'Gift Value 3 Should be a Number',
'Gift_Value_4Required'=>'Gift Value 4 is Required',
'Gift_Value_4Numeric'=>'Gift Value 4 Should be a Number',
'Gift_Value_5Required'=>'Gift Value 5 is Required',
'Gift_Value_5Numeric'=>'Gift Value 5 Should be a Number',
'Gift_Value_1'=>'Gift Value 1',
'Gift_Value_2'=>'Gift Value 2',
'Gift_Value_3'=>'Gift Value 3',
'Gift_Value_4'=>'Gift Value 4',
'Gift_Value_5'=>'Gift Value 5',
'Special_Gift_Order'=>'Special Gift Order',
'GiftValueRequired'=>'Gift Value is Required',
'Gift_Value'=>'Gift Value',
'No'=>'No',
'Still_Date_Not_Come'=>'I have not finished the date of attendance yet',
'NOT_Found'=>'Not Found',
'Blog_Details'=>'Blog Details',
'Attendence_Place'=>'Place of Attendence',
'Attendance_Confirmed'=>'Attendance Confirmed',
'Confirm_Attendance'=>'Confirm Attendance',
'RUSFromAttendence'=>'Are you sure you are now at the place of reservation? Your current location will be taken to confirm attendance, so make sure of your location until the confirmation is correct. Do you want to confirm now ?',
'Search'=>'Search',
'Search_Here'=>'Search Here',
'Subscribe_Blog_List'=>'Subscribe to the Blogs list',
'Subscribe_Blog_List_to_New_Blogs'=>'Subscribe to the Hoor Blogs list to receive all new:',
'Enter_Email'=>'Enter Your Email',
'Subscribes'=>'Subscribes',
'Title'=>'Title',
'Link'=>'Link',
'Send_Successfully'=>'Send Successfully',
'New_Blog'=>'New Blog',
'Blog_Link'=>'Blog Link',
'Send_New_Blog'=>'Send New Blog',
'Blogs_Subscribes'=>'Blogs Subscribes',
'NewAddQuestion'=>'Added Successfully',
'Hoor_Environment'=>'Hoor Environment',
'Create_Bill'=>'Create Bill',
'Search_Bill'=>'Search Bill',
'Tax_Num'=>'Taxes Number',
'time'=>'Time',
'Final_Save'=>'Final Savel',
'Change_Logo'=>'Change Logo',
'Cash'=>'Cash',
'Visa'=>'Visa',
'Save'=>'Save',
'Saved'=>'Saved',
'Not_Service'=>'Must Choice at Least One Service',
'Service_Time'=>'Service Time',
'Alert_Hoor_Env'=>'You must send and print before clicking on final save',
'Not_Found_Blog_Like_That'=>'Not Found Blog Like That',
'Not_Final_Save'=>'Temporarily saved To confirm saving the invoice, please click on  Save and Send',
'Tax_Type'=>'Taxes Type',
'Choice_Way'=>'Choose the appropriate method',
'Static_Number'=>'Static Number',
'Static_Rate'=>'Percent %',
'Export_PDF'=>'Export PDF',
'Save_Send'=>'Save and Send',
'Hoor_Plus'=>'Hoor Plus',
'Client_Name'=>'Client Name',
'Client_Phone'=>'Client Phone',
'Present_Service'=>'Present Service',
'Other'=>'Other',
'Notes'=>'Notes',
'Expert'=>'Expert',
'Personal_Details'=>'Personal Details',
'Confirm_Password_Isnt_Same'=>'Confirm Password Isnt Same',
'Reserv_Details'=>'Reservations Details',
'Created_Successfully'=>'Created Successfully',
'Confirm_Reserv'=>'Your reservation has been confirmed successfully',
'SpecialReserv'=>'Add Sepcial Reservations',
'Salon_or_Expert'=>'Salon or Expert',
'Special_Reservations'=>'Sepcial Reservations',
'Create_New_Reservations'=>'Create New Reservations',
'Hoor_Resrev'=>'Hoor Reservations',
'Other_Resrev'=>'Other Reservations',
'DateYesterday'=>'The reservation date must be a current or future date and not a past',
'Hoor_Offer_Resrev'=>'Hoor Offer Reservations',
'AlertOfTimes'=>'Must be Sure To Complete Data of Times to appaear Salon/Artist at Website',
'Account_Type'=>'Account Type',
'Main_Account'=>'Main Account',
'Main'=>'Main',
'Sub'=>'Sub',
'Currency'=>'Currency',
'Draw'=>'Draw',
'Cost_Center'=>'Cost Center',
'U_Cant_Delete_Any_Account_Has_Traffic'=>'You Cant Delete Any Account Has Childs',
'NameRequired'=>'Name is Required',
'AccountTypeRequired'=>'Account Type is Required',
'Debitor'=>'Debitor',
'Creditor'=>'Creditor',
'Account_Code'=>'Account Code',
'Account_Name'=>'Account Name',
'Statement'=>'Statement',
'User'=>'User',
'Time'=>'Time',
'Screen'=>'Screen',
'Type'=>'Type',
'From'=>'From',
'To'=>'To',
'Sort'=>'Sort',
'SaveandPrint'=>'Save & Print',
'Total_Debitor'=>'Total Debitor',
'Total_Creditor'=>'Total Creditor',
'Differnence'=>'Differnence',
'DateRequired'=>'Date is Required',
'CoinRequired'=>'Coin is Required',
'DrawRequired'=>'Draw is Required',
'Cost_CenterRequired'=>'Cost Center is Required',
'Bond_Type'=>'Bond Type',
'Coin'=>'Coin',
'Coin_Debitor'=>'Coin Debitor',
'Coin_Creditor'=>'Coin Creditor',
'Difference'=>'Difference',
'Numbers'=>'Numbers',
'Account'=>'Account',
'Details'=>'Details',
'Sechdules'=>'Sechdules',
'Amount_Coin'=>'Amount Coin',
'Edit_Journalizing'=>'Edit Journalizing',
'Code_Name'=>'Code/Name',
'Bond_Code'=>'Bond Code',
'Filter'=>'Filter',
'Search_for_Bond'=>'Search for Bond',
'Safe'=>'Safe',
'Safe_Balance'=>'Safe Balance',
'Capital'=>'Capital',
'Edit_Payment_Voucher'=>'Edit Payment Voucher',
'Edit_Receipt_Voucher'=>'Edit Recipt Voucher',
'Edit_Opening_Entries'=>'Edit Opening Entries',
'Check_Type'=>'Check Type',
'Bank'=>'Bank',
'Check_Num'=>'Check Number',
'Due_Date'=>'Due Date',
'Pay_Account'=>'Pay Account',
'Amount'=>'Amount',
'Coin_Amount'=>'Coin Amount',
'AccountRequired'=>'Account is Required',
'Check_TypeRequired'=>'Check Type is Required',
'BankRequired'=>'Bank is Required',
'Check_NumRequired'=>'Check Number is Required',
'Due_DateRequired'=>'Due Date is Required',
'AmountRequired'=>'Amount is Required',
'Refuse'=>'Refuse',
'Reason_Refuse'=>'Reason_Refuse',
'Transfer_to_IncomCheck'=>'Transfer to Incoming Check',
'Bene_Account'=>'Benefet Account',
'Refused'=>'Refused',
'Refuse_Checks'=>'Refuse Check',
'Transfer_Successfully'=>'Transfer Successfully',
'Transfer_to_Income'=>'Transfer to Incoming Check',
'Transfer_to_ExportCheck'=>'Transfer to Export Check',
'Waiting'=>'Waiting',
'Transfered'=>'Transfered',
'Arrest_Account'=>'Arrest Account',
'TypeRequired'=>'Type is Required',
'Check_Payed'=>'Check Payed',
'Pay'=>'Pay',
'Show'=>'Show',
'Zero_Balances'=>'Zero Balances',
'Debiator_Before'=>'Debiator Before',
'Creditor_Before'=>'Creditor Before',
'Debitor_Balance'=>'Debitor Balance',
'Creditor_Balance'=>'Creditor Balance',
'Total_Debiator_Before'=>'Total Debiator Before',
'Total_Debitor_Balance'=>'Total Debitor Balance',
'Total_Creditor_Before'=>'Total Creditor Before',
'Total_Creditor_Balance'=>'Total Creditor Balance',
'Checked_All'=>'Checked All',
'Cancel_All'=>'Cancel All Checked',
'Alll'=>'All',
'Check_Status'=>'Check Status',
'RateRequired'=>'Rate is Required',
'Taxes_TypeRequired'=>'Taxes Type is Required',
'Rate'=>'Rate',
'Ratio'=>'Ratio',
'Shipping_Company'=>'Shipping Company',
'Assets'=>'Assets',
'Rate_Type'=>'Rate Type',
'SaveandPrint8'=>'Save and Print 8CM',
'Taxes_Type'=>'Taxes Type',
'Precent'=>'Precent',
'Static_Number'=>'Static Number',
'Group'=>'Group',
'Main_Data'=>'Main Data',
'Sub_Data'=>'Sub Data',
'Completed'=>'Completed',
'Raw'=>'Raw',
'Service'=>'Service',
'Assembly'=>'Assembly',
'Industrial'=>'Industrial',
'Single_Variable'=>'Single Variable',
'Duble_Variable'=>'Duble Variable',
'Serial'=>'Serial',
'Product_Type'=>'Product Type',
'Product_Ar_Name'=>'Product Arabic Name',
'Product_En_Name'=>'Product English Name',
'Brand'=>'Brand',
'Minimum'=>'Minimum',
'Maximum'=>'Maximum',
'Length'=>'Length',
'Width'=>'Width',
'Height'=>'Height',
'Weight'=>'Weight',
'Saller_Point'=>'Saller Point',
'Customer_Point'=>'Customer Point',
'Tax'=>'Tax',
'Validity'=>'Validity',
'Days_Notify'=>'Days Notify',
'Ar_Desc'=>'Arabic Description',
'En_Desc'=>'English Description',
'Ar_Spec'=>'Arabic Specification',
'En_Spec'=>'English Specification',
'Store_Show'=>'Store Show',
'Store_Type'=>'Store Type',
'Sub_Images'=>'Sub Images',
'Unit'=>'Unit',
'Barcode'=>'Barcode',
'Price_One'=>'Price One',
'Price_Two'=>'Price Two',
'Price_Three'=>'Price Three',
'Recently'=>'Recently',
'Special'=>'Special',
'Finaly'=>'Finaly',
'Modules_Settings'=>'Modules Settings',
'Guide_Product_Cost'=>'Guide Product Cost',
'Client_Store_Account'=>'Client Store Account',
'Search_Typical'=>'Search Typical',
'Validity_Product'=>'Validity Product',
'P_TypeRequired'=>'Product Type is Required',
'P_Ar_NameRequired'=>'Product Arabic Name is Required',
'GroupRequired'=>'Group is Required',
'Search_For_Products'=>'Search For Products',
'Units'=>'Units',
'Qty'=>'Quantity',
'No_Found'=>'Not Found',
'Desc'=>'Description',
'Spec'=>'Specification',
'P_Type_Details'=>'Product Type Details',
'QrCode'=>'Qr Code',
'P_Code'=>'Product Code',
'P_Type'=>'Product Type',
'P_Ar_Name'=>'Product Arabic Name',
'P_En_Name'=>'Product English Name',
'Active'=>'Active',
'UnActive'=>'UnActive',
'Edit_Items'=>'Edit Items',
'Store'=>'Store',
'Total_Products'=>'Total Products',
'Total_Qty'=>'Total Qty',
'Total_Price'=>'Total Price',
'StoreRequired'=>'Store is Required',
'TotalProductsRequired'=>'Total Products is Required',
'TotalQtyRequired'=>'Total Qty is Required',
'TotalPriceRequired'=>'Total Price is Required',
'Virable'=>'Virable',
'Cost'=>'Cost',
'Exp_Date'=>'Expire Date',
'Subscribe'=>'Subscribe',
'Edit_Start_Period'=>'Edit Start Period',
'Can_not_Choice_Same_Virables'=>'Can not Choice Same Virable',
'End_Subscribe_Date'=>'End Subscribe Date',
'Sub_Cost'=>'Subscribe Cost',
'Subscribe_Type'=>'Subscribe Type',
'Inventory'=>'Inventory',
'Account_Dificit'=>'Account Dificit',
'Account_Excess'=>'Account Excess',
'Default_Unit'=>'Default Unit',
'Dificit'=>'Dificit',
'Execess'=>'Execess',
'Settlement'=>'Settlement',
'Edit_Inventory'=>'Edit Inventory',
'Total_Dificit'=>'Total Dificit',
'Total_Execess'=>'Total Execess',
'Total_Dificit_Price'=>'Total Dificit Price',
'Total_Execess_Price'=>'Total Execess Price',
'Inventory_Sechdule'=>'Inventory Sechdule',
'Settlement_Sechdule'=>'Settlement Sechdule',
'Total_DificitRequired'=>'Total Dificit is Required',
'Total_ExcessRequired'=>'Total Excess is Required',
'Total_Dificit_PriceRequired'=>'Total Dificit Price is Required',
'Total_Excess_PriceRequired'=>'Total Excess Price is Required',
'Account_DificitRequired'=>'Account Dificit is Required',
'Account_ExcessRequired'=>'Account Excess is Required',
'Dificit_Settlement'=>'Dificit Settlement',
'Execess_Settlement'=>'Execess Settlement',
'Safes_Transfer'=>'Safes Transfer',
'From_Safe'=>'From Safe',
'To_Safe'=>'To Safe',
'Update'=>'Update',
'Home'=>'Home',
'From_Store'=>'From Store',
'To_Store'=>'To Store',
'Total_Price_Trans_Qty'=>'Total Price Transfer Quantity',
'Total_Trans_Qty'=>'Total Transfer Quantity',
'Trans_Qty'=>'Transfer Quantity',
'Safes_Transfer_Sechdule'=>'Safes Transfer Sechdule',
'Type'=>'Type',
'Direction'=>'Direction',
'Width'=>'Width',
'Height'=>'Height',
'Padding_L'=>'Padding Left',
'Padding_R'=>'Padding Right',
'Padding_T'=>'Padding Top',
'Padding_B'=>'Padding Bottom',
'Margin_L'=>'Margin Left',
'Margin_R'=>'Margin Right',
'Margin_T'=>'Margin Top',
'Margin_B'=>'Margin Bottom',
'Barcode_Width'=>'Barcode Width',
'Barcode_Height'=>'Barcode Height',
'Font_Size'=>'Font Size',
'Line_Height'=>'Line Height',
'A4'=>'A4',
'Pulley'=>'Pulley',
'Portrait'=>'Portrait',
'Landscape'=>'Landscape',
'Style'=>'Style',
'Show_in_Print'=>'Show in Print',
'Company_Name'=>'Company Name',
'Product_Name'=>'Product Name',
'Product_Price'=>'Product Price',
'DaysNumbersRequired'=>'Days Numbers is Required',
'CostHourRequired'=>'Cost Hour is Required',
'DaysNumbers'=>'Days Numbers',
'Cost_Hour'=>'Cost Hour',
'Salary'=>'Salary',
'Department'=>'Department',
'Job'=>'Job',
'Emp_Type'=>'Employee Type',
'Manager'=>'Manager',
'Saller'=>'Saller',
'Buyer'=>'Buyer',
'Driver'=>'Driver',
'Engineer'=>'Engineer',
'Techinical'=>'Techinical',
'Delivery'=>'Delivery',
'Designer'=>'Designer',
'Programmer'=>'Programmer',
'Accountant'=>'Accountant',
'Store_Keeper'=>'Store Keeper',
'Observer_Quality'=>'Observer Quality',
'Supervisor'=>'Supervisor',
'Doctor'=>'Doctor',
'Teacher'=>'Teacher',
'Worker'=>'Worker',
'Nurse'=>'Nurse',
'Cashier'=>'Cashier',
'Chef'=>'Chef',
'Secuirty'=>'Secuirty',
'Waiter'=>'Waiter',
'Secuirty'=>'Secuirty',
'Attendence'=>'Attendence',
'Departure'=>'Departure',
'Hours_Numbers'=>'Hours Numbers Per Month',
'Days_Numbers'=>'Days Numbers',
'Day_Price'=>'Day Price',
'Precentage_of_Sales'=>'Precentage of Sales',
'Precentage_of_Profits'=>'Precentage of Profits',
'Bank_Account'=>'Bank Account',
'Qualifications'=>'Qualifications',
'Address'=>'Address',
'Social_Status'=>'Social Status',
'ID_Number'=>'ID Number',
'Contract_Start'=>'Contract Start',
'Contract_End'=>'Contract End',
'Phone2'=>'Phone 2',
'Account_Emp'=>'Account Employee',
'NameRequired'=>'Name is Required',
'Emp_TypeRequired'=>'Employee Type is Required',
'SalaryRequired'=>'Salary is Required',
'JobRequired'=>'Job is Required',
'DepartmentRequired'=>'Department is Required',
'AccountRequired'=>'Account is Required',
'You_Cant_Delete_This_Emp_Can_Delete_From_Emp_Sechdule_Just'=>'You Cant Delete This Employe from Here You Can Delete him From Employes Sechdule',
'You_Cant_Delete_This_Client_Can_Delete_From_Client_Sechdule_Just'=>'You Cant Delete This Client from Here You Can Delete him From Clients Sechdule',
'Commercial_Register'=>'Commercial Register',
'Tax_Card'=>'Tax Card',
'Price_Level'=>'Price Level',
'Level_1'=>'Level 1',
'Level_2'=>'Level 2',
'Level_3'=>'Level 3',
'Payment_Method'=>'Payment Method',
'Cash'=>'Cash',
'Account_Credit'=>'Account Credit',
'Later'=>'Later',
'Recived'=>'Recived',
'Pending'=>'Pending',
'Delegate'=>'Delegate',
'Vendor_Bill_Date'=>'Vendor Bill Date',
'Refrence_Number'=>'Refrence Number',
'Discount'=>'Discount',
'Product_Numbers'=>'Product Numbers',
'Total_Discount'=>'Total Discount',
'Total_Bf_Taxes'=>'Total before Taxes',
'Total_Taxes'=>'Total Taxes',
'Paid'=>'Paid',
'Refernce_Number'=>'Refernce Number',
'Vendor'=>'Vendor',
'Convert'=>'Convert',
'Transfer_To_Purchases'=>'Transfer To Purchases',
'Total_Tax'=>'Total Tax',
'Recived_Purchases'=>'Recived Purchases Bill',
'EditPurchasesOrder'=>'Edit Purchases Order',
'Total_Trans_Qty'=>'Total Transfer Quantity',
'Total_Trans_Value'=>'Total Transfer Value',
'Original_Qty'=>'Original Qty',
'Recived_Qty'=>'Recived Qty',
'Recived'=>'Recived',
'Recivedd'=>'Recived',
'Total_Bf_Tax'=>'Total Before Tax',
'Total_Recived_Qty'=>'Total Recived Qty',
'Purchase_Bill_Number'=>'Purchase Bill Number',
'Return_Purchases'=>'Return Purchases',
'Return_Qty'=>'Return Qty',
'Returnn'=>'Return',
'Total_Return_Qty'=>'Total Return Qty',
'Total_Return_Value'=>'Total Return Value',
'Return_Successfully'=>'Returned Successfully',
'Total_BF_Taxes'=>'Total Before Taxes',
'Width_Logo'=>'Width Logo',
'Height_Logo'=>'Height Logo',
'Responsible'=>'Responsible',
'Next_Appointment'=>'Next Appointment',
'Tickets'=>'Tickets',
'Level1'=>'Level 1',
'Level2'=>'Level 2',
'Level3'=>'Level 3',
'ClientStatus'=>'Client Status',
'Campagin'=>'Campagin',
'Activity'=>'Activity',
'Executions_Status'=>'Executions Status',
'potential_client'=>'Potential Client',
'new_client'=>'New Client',
'pending'=>'Pending',
'former'=>'Former',
'important'=>'Important',
'Birthdate'=>'Birthdate',
'Passport_Number'=>'Passport Number',
'Contract_Strat'=>'Contract Strat',
'Commercial_Registration_No'=>'Commercial Registration Number',
'Tax_Card_No'=>'Tax Card Number',
'Client_Paper'=>'Client Paper',
'Edit_Clients'=>'Edit Clients',
'Download'=>'Download',
'Platform'=>'Platform',
'Problem'=>'Problem',
'Bill_Number'=>'Bill Number',
'UnSolve'=>'UnSolve',
'Solved'=>'Solved',
'InterviewType'=>'Interview Type',
'Not_Done'=>'Not Done',
'Done'=>'Done',
'Rated'=>'Rated',
'Employee'=>'Employee',
'Client'=>'Client',
'Rateee'=>'Rate',
'Installment'=>'Installment',
'Executor'=>'Executor',
'AvQty'=>'Avilable Qty',
'Presenter'=>'Presenter',
'annual_interest'=>'Annual Interest',
'monthly_installment'=>'Monthly Installment',
'Years_Number'=>'Number of Years',
'total'=>'Total',
'EditSalesOrder'=>'Edit Sales Order',
'EditQuote'=>'Edit Quote',
'Transfer_to_Sales'=>'Transfer to Sales',
'Transfer_to_SalesOrder'=>'Transfer to Sales Order',
'Residual'=>'Residual',
'Emp_Borrow'=>'Employee Borrow',
'Total_Borrow'=>'Total Borrow',
'Borroww'=>'Borrow',
'Num_of_Days'=>'Number of Days',
'Start_Date'=>'Start Date',
'Borrow_Month'=>'Borrow Month',
'RsduialQty'=>'Rsduial Qty',
'Date_Install'=>'Date Installment',
'Value_Install'=>'Value Installment',
'Transfered'=>'Transfered',
'AddLoan'=>'Add Loan',
'Loan'=>'Loan',
'Products_Units'=>'Products Units',
'Upload'=>'Upload',
'Groups'=>'Groups',
'Reports'=>'Reports',
'Minimum_Limit'=>'Minimum Limit',
'Product_Order_Limit'=>'Product Order Limit',
'Product_Info'=>'Product Information',
'Export'=>'Export Excel',
'ExportProducts'=>'Export Excel',
'Attendence_Discount'=>'Attendence Discount',
'Holiday_Discount'=>'Holiday Discount',
'Holidays_Per_Days'=>'Holidays Per Days',
'Loan_Installment'=>'Loan Installment',
'Attendence_Hours'=>'Attendence Hours',
'Resduial_Salary'=>'Resduial Salary',
'AddSalary'=>'Add Salary',
'SalarySechdules'=>'Salary Sechdules',
'Loan_Code'=>'Loan Code',
'Settlement_Type'=>'Settlement Type',
'helpless'=>'Helpless',
'Avg_Purch'=>'Average Purchaces',
'Cost'=>'Cost',
'Period'=>'Period',
'Days'=>'Days',
'Bill_Num'=>'Bill Number',
'Incom'=>'Incom',
'Outcom'=>'Outcom',
'Total_Incom'=>'Total Incom',
'Total_Outcom'=>'Total Outcom',
'Balance'=>'Balance',
'StoresBalances'=>'Stores Balances',
'ItemsMoves'=>'Items Moves',
'Maximum_Sales'=>'Maximum Sales',
'Num_of_Sales'=>'Number of Sales',
'StagnantItems'=>'Stagnant Items',
'Products_Num'=>'Products Numbers',
'Total_Qty'=>'Total Qty',
'Total_Cost'=>'Total Cost',
'NetPurchases'=>'Net Purchases',
'NetSales'=>'Net Sales',
'TotalNetPurchases'=>'Total Net Purchases',
'TotalNetSales'=>'Total Net Sales',
'Total_Sales'=>'Total Sales',
'Total_Return_Sales'=>'Total Return Sales',
'increase'=>'Increase',
'Check'=>'Check',
'Bill_Num'=>'Bill Number',
'Total_Return'=>'Total Returns',
'Total_Bill_Num'=>'Total Bills Number',
'Total_Purch'=>'Total Purchases',
'Total_Return_Purch'=>'Total Return Purchases',
'EmpInstallment'=>'Employee Installments',
'Emp_Loan'=>'Employee Loan',
'InstallmentDone'=>'Installment Done',
'UnInstallmentDone'=>'Installment Not Done',
'installment_Num'=>'Installment Numbers',
'Date_First_installment'=>'Date First Installment',
'Precentage_of_Execution'=>'Precentage of Execution',
'Done_Do_U_Want_Change_Status_to_not_click_here'=>'(Done) Do You Want Change Status to not Done Click Here',
'Asset_Type'=>'Asset Type',
'Depreciation_Method'=>'Depreciation Method',
'Purchases_Date'=>'Purchases Date',
'Operation_Date'=>'Operation Date',
'Previous_Depreciation'=>'Previous Depreciation',
'Asset_Net'=>'Asset Net',
'Annual_Depreciation_Ratio'=>'Annual Depreciation Ratio',
'Annual_Depreciation'=>'Annual Depreciation',
'Life_Span'=>'Life Span',
'Depreciation_Expenses'=>'Depreciation Expenses',
'Depreciation_Complex'=>'Depreciation Complex',
'Main_Account'=>'Main Account',
'consumer'=>'Consumer',
'unconsumed'=>'Un Consumed',
'Fixed'=>'Fixed',
'growing'=>'Growing',
'decreasing'=>'Decreasing',
'Company_Data'=>'Company Data',
'Company_Name'=>'Company Name',
'Company_Address'=>'Company Address',
'Commercial_Record'=>'Commercial Record',
'Tax_File_Number'=>'Tax File Number',
'Print_Text'=>'Print Text',
'Logo'=>'Logo',
'Icon'=>'Icon',
'Price_Sale'=>'Price Sale',
'Open'=>'Open',
'VendorRequired'=>'Vendor is Required',
'ClientRequired'=>'Client is Required',
'Disabled'=>'Disabled',
'Code_Send'=>'Code Send Successfully',
'LoginSuccess'=>'Login Success',
'Mainus'=>'Sale by Mainus',
'Vendor_And_Clients'=>'Vendor And Clients',
'Purchases_Asset'=>'Purchases Asset',
//============
'Admins'=>'Admins',
'Requests'=>'Requests',
'Client_Address'=>'Client Address',
'Apps'=>'Apps',
'End_Meeting_Successfully'=>'End Meeting Successfully',
'cant_let_biref_empty'=>'cant let biref empty',
'OldPasswordNotCorrect'=>'Old Password Not Correct',
'LogoutSuccess'=>'Logout Success',
'success_reset'=>'Success Reset Password',
'Code_Success'=>'Code Success',
'WrongCode'=>'Wrong Code',
'ResetPasswordApi'=>'Reset Password',
'All'=>'All',
'Orders'=>'Orders',
'Pending'=>'Pending',
'RecivedShipComp'=>'Recived Shipping Company',
'RecivedClient'=>'Recived Client',
'Interview_Biref'=>'Interview Biref',
'VisitNum'=>'Visit Number',
'BillsNum'=>'Bills Number',
'BillsTotal'=>'Bills Total',
'Client_Status'=>'Client Status',
'Metting_Type'=>'Metting Type',
'Employes'=>'Employes',
'Governrates'=>'Governrates',
'Cities'=>'Cities',
'Insurance_Paper'=>'Insurance Paper',
'Insurance_Paper_Recived'=>'Recived Insurance Paper',
'Product_Type_Default'=>'Product Type Default',
'Product_Type_Use'=>'Product Type Use',
'ReciptMaintainceSechdule'=>'Recipt Maintaince Sechdule',
'ErrorType'=>'Error Type',
'Depreciation'=>'Depreciation',
'Asset'=>'Asset',
'Partner'=>'Partner',
'Remaining_Profit'=>'Remaining Profit',
'Remaining_Profits'=>'Remaining Profits',
'Capital'=>'Capital',
'Authorized_Capital'=>'Authorized Capital',
'Source_Capital'=>'Source Capital',
'Shares_Number'=>'Shares Number',
'Nominal_Value_of_Shares'=>'Nominal Value of Shares',
'Actual_Share_Value'=>'Actual Share Value',
'Actual_Capital'=>'Actual Capital',
'Partners'=>'Partners',
'Profits_Precentage'=>'Profits Precentage',
'Withdraw_Profits'=>'Withdraw Profits',
'Remaining_Profits'=>'Remaining Profits',
'Spend_Profits'=>'Spend Profits',
'ExchangeGoodsSechdule'=>'Exchang Goods Sechdule',
'Exchange_Goods'=>'Exchange Goods',
'Recived_Goods'=>'Recived Goods',
'ReceiveGoodsSechdule'=>'Recived Goods Sechdule',
'Have_Store'=>'Have Store ?',
'Permission_to_exchange_goods'=>'Permission to exchange goods',
'Permission_to_receive_goods'=>'Permission to receive goods',
'AssetExpenses'=>'Asset Expenses',
'MaintainceBill'=>'Maintaince Bill',
'Error_Nums'=>'Error Numbers',
'Device_Case'=>'Device Case',
'Errors'=>'Errors',
'Backup'=>'Backup Data Base',
'DBBackupSuccessfully'=>'Data Base Backup Daily Successfully',
'Income'=>'Income',
'Logoutt'=>'Logout Now',
'Product_Moves'=>'Product Moves',
'Hall'=>'Hall',
'Vist_Num'=>'Vist Numbers',
'Bills_Num'=>'Bills Numbers',
'Total_Sales'=>'Total Sales',
'Total_Get'=>'Total Get',
'Taransfer_Sure'=>'Taransfer Sure',
'Transfer_Sure'=>'Taransfer Sure',
'Damage'=>'Damage',
'ExecutingandReceiving'=>'Executing and Receiving',
'ManufacturingModelSechdule'=>'Manufacturing Model Sechdule',
'Depreciation_Precent'=>'Depreciation Precent',
'Except_Qty'=>'Except Qty',
'Depreciation'=>'Depreciation',
'Outcome'=>'Outcome',
'ManufacturingModel'=>'Manufacturing Model',
'Manufacturing'=>'Manufacturing',
'ManufacturingHalls'=>'Manufacturing Halls',
'Company'=>'Company',
'Goods_Cost'=>'Goods Cost',
'MaintainceBillSechdule'=>'Maintaince Bill Sechdule',
'Total_Price_Errors'=>'Total Price Errors',
'Serial_Num'=>'Serial Number',
'Manu_Company'=>'Manufactur Company',
'Device_Type'=>'Device Type',
'Maintenance'=>'Maintenance',
'DesviceCases'=>'Desvice Cases',
'FaultsType'=>'Faults Type',
'ReciptMaintaince'=>'Recipt Maintaince',
'DevicesTypes'=>'Devices Types',
'ManufacturCompany'=>'Manufactur Company',
'ProblemRequired'=>'Problem is Required',
'Location_Updated'=>'Location Updated',
'Bill_Nums'=>'Bill Numbers',
'MettingsTypes'=>'Mettings Types',
'Previous_Interviews'=>'Previous Interviews',
'MyMettings'=>'My Mettings',
'Just_Emp'=>'This Page just for Employees',
'Credit'=>'Credit',
'Emp_Locations'=>'Employees Locations',
'Location'=>'Locations',
'AdminsPremations'=>'Users Premations',
'Accounts'=>'Accounts',
'Default_Data'=>'Default Data',
'Profits'=>'Profits',
'Check_Number'=>'Check_Number',
'Shipping_Compaines'=>'Shipping Compaines',
'StoresCost'=>'Stores Cost',
'Accounting_Manual'=>'Accounting Manual',
'Cost_Centers'=>'Cost Centers',
'Coins'=>'Coins',
'Journalizing'=>'Journalizing',
'General_Daily'=>'General Daily',
'Owner_Reports'=>'Owner Reports',
'User_Log'=>'User Log',
'Accounts_Reports'=>'Accounts Reports',
'Receipt_Voucher'=>'Receipt Voucher',
'Payment_Voucher'=>'Payment Voucher',
'Opening_Entries'=>'Opening Entries',
'Checks_Type'=>'Checks Type',
'Exporting_Checks'=>'Exporting Checks',
'Incoming_checks'=>'Incoming Checks',
'Safes_Banks'=>'Safes & Banks',
'Trial_Balance'=>'Trial Balance',
'Ledger'=>'Ledger',
'Account_Balances'=>'Account Balances',
'Safe_Bank_Statement'=>'Safe & Bank Statement',
'Checks_Reports'=>'Checks Reports',
'Vendor_Account_Statement'=>'Vendor Account Statement',
'Customer_Account_Statement'=>'Customer Account Statement',
'Customer_Balances'=>'Customer Balances',
'Cost_Centers_Report'=>'Cost Centers Report',
'Journalizing_Sechdule'=>'Journalizing Sechdule',
'Opening_Entries_Sechdule'=>'Opening Entries Sechdule',
'Receipt_Voucher_Sechdule'=>'Receipt Voucher Sechdule',
'Payment_Voucher_Sechdule'=>'Payment Voucher Sechdule',
'Incom_List'=>'Incom List',
'Financial_Center'=>'Financial Center',
'Stores'=>'Stores',
'Measurement_Units'=>'Measurement Units',
'Manufacture'=>'Manufacture',
'Virables'=>'Virables',
'Sub_Virables'=>'Sub Virables',
'Taxes'=>'Taxes',
'HR'=>'HR',
'Holidays_Types'=>'Holidays Types',
'Overtime'=>'Overtime',
'Add_Items'=>'Add Items',
'Items_Groups'=>'Items Groups',
'Products_Sechdule'=>'Products Sechdule',
'Start_Period_Products'=>'Start Period Products',
'SubscribeTypes'=>'Subscribe Types',
'Start_Period_Sechdule'=>'Start Period Sechdule',
'Items_Guide'=>'Items Guide',
'Stores_Transfers'=>'Stores Transfers',
'Stores_Transfers_Sechdule'=>'Stores Transfers Sechdule',
'BarcodeـPrinting'=>'Barcode Printing',
'BarcodeـPrinting_Settings'=>'Barcode Printing Settings',
'Work_Departments'=>'Work Departments',
'Jobs_Type'=>'Jobs Type',
'Benefits_Types'=>'Benefits Types',
'Deductions_Types'=>'Deductions Types',
'Add_Emp'=>'Add Employee',
'Emp_Sechdule'=>'Employee Sechdule',
'Purchases'=>'Purchases',
'Vendors'=>'Vendors',
'PurchasesHold'=>'Purchases Hold',
'PurchasesOrder'=>'Purchases Order',
'PurchasesOrderSechdule'=>'Purchases Order Sechdule',
'PurchasesSechdule'=>'Purchases Sechdule',
'Recived_Products'=>'Recived Products',
'Sales'=>'Sales',
'Translate'=>'Translate',
'Clients'=>'Clients',
'Governrate'=>'Governrate',
'City'=>'City',
'Cities'=>'Cities',
'CRM'=>'CRM',
'Borrow'=>'Borrow',
'AddBorrow'=>'Add Borrow',
'Loan_Types'=>'Loan Types',
'Return_Sales'=>'Return Sales',
'Return_Sales_Sechdule'=>'Return Sales Sechdule',
'Recived_Sales'=>'Recived Sales',
'SalesHoldSechdule'=>'Sales Hold Sechdule',
'Sales_Bill_Num'=>'Sales Bill Number',
'Transfer_to_SalesOrder'=>'Transfer to Sales Order',
'SalesOrderSechdule'=>'Sales Order Sechdule',
'Interviews_Types'=>'Interviews Types',
'Interviews'=>'Interviews',
'Platforms'=>'Platforms',
'SalesOrder'=>'Sales Order',
'Campaigns'=>'Campaigns',
'Activites'=>'Activites',
'Quote'=>'Quote',
'EditDeparture'=>'Edit Departure',
'Attendance'=>'Attendance',
'Departure'=>'Departure',
'Hours_Number'=>'Hours Number',
'Attendance_Time'=>'Attendance Time',
'Dep_Time'=>'Departure Time',
'Code_Attend'=>'Code Attendance',
'EditAttendance'=>'Edit Attendance',
'AttendanceSechdule'=>'Attendance Sechdule',
'DepartureSechdule'=>'Departure Sechdule',
'ConverteOrder'=>'Converte Order',
'Converted'=>'Converted',
'HolidaysOrder'=>'Holidays Order',
'Entitlements'=>'Entitlements',
'Deducation'=>'Deducation',
'Holidays'=>'Holidays',
'Discount_Salary'=>'Discount Salary',
'InstallmentSechdule'=>'Installment Sechdule',
'SalesSechdule'=>'Sales Sechdule',
'Quote_Sechdule'=>'Quote Sechdule',
'Add_Clients'=>'Add Clients',
'Hour_Rate'=>'Hour Rate',
'Total_Hours'=>'Total Hours',
'Hour_Cost'=>'Hour Cost',
'SettlementsReports'=>'Settlements Reports',
'ReportStartPeriodProducts'=>'Report Start Period Products',
'Reg_OverTime'=>'Register Over Time',
'Clients_Status'=>'Clients Status',
'ReturnPurchasesSechdule'=>'Return Purchases Sechdule',
'Sort_Asset'=>'Sort Asset',
'Start_Period'=>'Start Period',
'Purchase'=>'Purchase',
'Holding'=>'Holding',
'Holding_Bill'=>'Holding Bill',
'Asset_Sale'=>'Asset Sale',
'Closed'=>'Closed',
'Shifts'=>'Shifts',
'Dif'=>'Differnece',
'SaveandPrintA5'=>'Save and Print A5',
'Emp'=>'Employee',
'Cost_Stores'=>'Cost Stores',
'Qty_Stores'=>'Qty in All Stores',
'Print_Text_Footer'=>'Print Text Footer',
'pos_pay'=>'Pay Button at POS Screen',
'Shifts_Details'=>'Shifts Details',
'DailyClosing'=>'Daily Closing',
'Sale_Prices'=>'Sale Prices',
'RUSWCO'=>'Are You Sure Want to Cancel Order ?',
'RUSWHB'=>'Are You Sure Want to Hold Order ?',
'YesandPrint'=>'Yes and Print',
'Print_Text_Footer_Sales'=>'Print Text Footer Sales',
'YesandPrint8'=>'Yes and Print 8CM',
'A4_Sales_Print'=>'A4 Sales Print',
'A5_Sales_Print'=>'A5 Sales Print',
'8CM_Sales_Print'=>'8CM Sales Print',
'NameRequired'=>'Name is Required',
'Projects'=>'Projects',
'End_Date'=>'End Date',
'Duration'=>'Duration',
'Not_Found'=>'Not Found',
'Team'=>'Team',
'StatusNote'=>'Biref',
'PerivousMettings'=>'Perivous Mettings',
'Missions'=>'Missions',
'MyMissions'=>'My Missions',
'Task_OwnerRequired'=>'Task Owner is Required',
'ObserverRequired'=>'Observer is Required',
'ProjectRequired'=>'Project is Required',
'Task_Owner'=>'Task Owner',
'Observer'=>'Observer',
'Project'=>'Project',
'DurationRequired'=>'Duration is Required',
'ValueRequired'=>'Value is Required',
'ClientRequired'=>'Client is Required',
'ManagerRequired'=>'Manager is Required',
'Startdate'=>'Start Date',
'Startafter'=>'Start Date should be before end date',
'EndRequired'=>'End Date is Required',
'Enddate'=>'End Date',
'Cant_Delete_Default_Data'=>'Can not Delete Default Data',
'Cant_Delete_User_Admin'=>'Can not Delete User Delete User in Admins First',
'ClientSales'=>'Client Sales',
'Endafter_or_equal'=>'End Date must be after start date or equal',
'infinite'=>'infinite',
'Limit_Num'=>'Limit Number',
'Branches_Num'=>'Branches Number',
'Stores_Num'=>'Stores Number',
'Users'=>'Users',
'Subscribe_Date'=>'Subscribe Date',
'Subscribe_Type'=>'Subscribe Type',
'SalesSubscribes'=>'Sales Subscribes',
'Installment'=>'Installment',
'Discount_Bill'=>'Discount on Bill',
'Total_af_Discount'=>'Total after Discount',
'Alert_Maximum_Add'=>'Alert Maximum Add',
'Users_Num'=>'Users Number',
'Sub_Date'=>'Subscribe Date',
'Sub_Type'=>'Subscribe Type',
'Oil'=>'Oil',
'Recived'=>'Recived',
'Print_Text_Footer_Secretariat'=>'Print Text Footer Recived Secretariat Goods',
'Impotence'=>'Impotence',
'VendorPurchases'=>'Vendor Purchases',
'Original_Name'=>'Original Name',
'Store_Goods'=>'Store Goods',
'New_Name_in_Arabic'=>'New Name in Arabic',
'New_Name_in_English'=>'New Name in English',
'EditSalesSubscribes'=>'Edit Sales Subscribes',
'SalesSubscribesSechdule'=>'Sales Subscribes Sechdule',
'Recived_Secretariat_Export_goods'=>'Recived Secretariat Export goods',
'Ship_Price'=>'Ship Price',
'Shipping_Company'=>'Shipping Company',
'Places'=>'Places',
'Place'=>'Place',
'Addresses'=>'Addresses',
'Street'=>'Street',
'Buliding'=>'Buliding',
'Floor'=>'Floor',
'Petrol'=>'Petrol',
'Counter'=>'Counter',
'Workers'=>'Workers',
'Pervious_Read'=>'Pervious Read',
'Current_Read'=>'Current Read',
'Consumption'=>'Consumption',
'Bones'=>'Bones',
'EditSalesPetrol'=>'Edit Sales Petrol',
'Reciptss'=>'Reciptss',
'SalesPetrolSechdule'=>'Sales Petrol Sechdule',
'Calibers'=>'Calibers',
'Supply_Sechdule'=>'Supply Sechdule',
'SalesPetrol'=>'Sales Petrol',
'Petroll'=>'Petrol',
'EditPurchasePetrol'=>'Edit Purchase Petrol',
'PurchasePetrolSechdule'=>'Purchase Petrol Sechdule',
'Delivery_Method'=>'Delivery Method',
'Full'=>'Full Petrol ?',
'PurchasePetrol'=>'Purchase Petrol',
'Current_Read'=>'Current Read',
'FuelStation'=>'Fuel Station',
'Patrol_Type'=>'Patrol Type',
'CountersType'=>'Counters Type',
'CompanyCars'=>'Company Cars',
'BonesType'=>'Bones Type',
'ReciptsType'=>'Recipts Type',
'Car_Number'=>'Car Number',
'MyRequestDelivery'=>'My Request Delivery',
'Version'=>'Version',
'DataBases'=>'Data Bases',
'BackupDB'=>'Backup Data Base',
'ExpiredProucts'=>'Expired Proucts',
'Total_Bf_Delivery'=>'Total Before Delivery',
'Delivery_Amount'=>'Delivery Amount',
'Total_Af_Delivery'=>'Total After Delivery',
'Logo_Store'=>'Logo Store',
'Totuch_Screen'=>'Totuch Screen',
'Flat'=>'Flat',
'Total_Price_Sale1'=>'Total Price Sale 1',
'Total_Price_Sale2'=>'Total Price Sale 2',
'Total_Price_Sale3'=>'Total Price Sale 3',
'Total_Profit'=>'Total Profit',
'Precnet'=>'Precnet',
'Resdiual'=>'Resdiual',
'ExceptProfits'=>'Except Profits',
'InstallmentReport'=>'Installment Report',
'Special_Mark'=>'Special Mark',
'Location'=>'Location',
'Employess'=>'Employess',
'Collection'=>'Collection',
'Manufacturing_Type'=>'Manufacturing Type',
'Big'=>'Big',
'ArabicCode'=>'Dont Enter any Arabic Word or Number in Code Input',
'Small'=>'Small',
'Shipping_Precent'=>'Shipping Precent',
'DB_Backup'=>'DB Backup',
'Recived_Name'=>'Recived Name',
'Recived_Signture'=>'Recived Signture',
'Execute_Precent'=>'Execute Precent',
'Servicee'=>'Service',
'Total_Cost'=>'Total Cost',
'Deliverd'=>'Deliverd',
'TotalDailyMoves'=>'Total Daily Moves',
'SalesDeliverySechdule'=>'Sales Delivery Sechdule',
'ExecutorSales'=>'Executor Sales',
'Executor_Sale'=>'Executor Sale',
'Address_Details'=>'Address Details',
'pos_product'=>'Add Products to POS Bill',
'DUWTMVoucher'=>'Do you Want to Make Recipt Voucher?',
'POSStores'=>'POS Stores',
'Saler'=>'Saler',
'Rounding'=>'Rounding',
'One_Num'=>'One Number',
'Two_Num'=>'Two Number',
'Right_Num'=>'Right Number',
'Storee'=>'Web Store',
'Sales_Show'=>'Sales Show',
'MustChoiceBarcode'=>'Must Choice Barcode',
'Casher'=>'Casher',
'Phone3'=>'Phone 3',
'Phone4'=>'Phone 4',
'Price_With_Tax'=>'Price With Tax',
'OriginalPrice'=>'Original Price',
'UnActived'=>'Un Actived',
'Actived'=>'Actived',
'ECommerce'=>'E-Commerce',
'Users_Web'=>'Users Website',
'Website'=>'Website',
'Sub_Image'=>'Sub Image',
'MsgRqst'=>'Messageg Rquest',
'Polices'=>'Polices',
'Image_2'=>'Image 2',
'Image_3'=>'Image 3',
'CouponCode'=>'Coupon Code',
'Opening_Hours'=>'Opening Hours',
'Phone_Header'=>'Phone Header',
'Map'=>'Map',
'FAQ'=>'FAQ',
'EComDesign'=>'E-Commercce Design',
'Cart_is_Empty'=>'Cart is Empty',
'Billing_Details'=>'Billing Details',
'Cart_Totals'=>'Cart Totals',
'Apply_Coupon'=>'Apply Coupon',
'Related_Product'=>'Related Product',
'Special_Offer'=>'Special Offer',
'New_Arrivals'=>'New Arrivals',
'Countris'=>'Countries',
'ProDetailsImg'=>'Ads Images',
'BefroeFooter'=>'Befroe Footer',
'Num'=>'Number of Use',
'Arabic_Question'=>'Arabic Question',
'English_Question'=>'English Question',
'Arabic_Answer'=>'Arabic Answer',
'English_Answer'=>'English Answer',
'Flag'=>'Flag',
'Icon_Store'=>'Icon Store',
'View'=>'View',
'Default'=>'Default',
'Offer'=>'Offer',
'Country'=>'Country',
'Shop'=>'Shop',
'Login'=>'Login',
'Register'=>'Register',
'MyAccount'=>'My Account',
'Logout'=>'Logout',
'Forgot_your_password'=>'Forgot your password',
'Registerd_Successfully'=>'Registerd Successfully',
'Blogs'=>'Blogs',
'Submit'=>'Submit',
'CurrentPassWrong'=>'Current Password Wrong',
'Blogs_Details'=>'Blogs Details',
'Send_Message'=>'Send Message',
'Latest_Blog'=>'Latest Blog',
'Read_more'=>'Read More',
'Change_Password'=>'Change Password',
'Quick_Shop'=>'Quick Shop',
'Informations'=>'Informations',
'Most_Salling'=>'Most  Salling',
'New_Password'=>'New Password',
'Confirm_Password'=>'Confirm Password',
'CurrentPassword'=>'Current Password',
'Our_Proucts'=>'Our Proucts',
'Symbol'=>'Symbol',
'Add_to_Compare'=>'Add to Compare',
'Add_To_Cart'=>'Add To Cart',
'Add_to_Cart'=>'Add To Cart',
'If_Offer'=>'If Offer',
'OfferPrice'=>'Offer Price',
'Wishlist'=>'Wishlist',
'Compare'=>'Compare',
'Ratees'=>'Rates',
'In_Stock'=>'In Stock',
'Out_Stock'=>'Out Stock',
'Product'=>'Product',
'Write_Comment'=>'Write Comment',
'Related_Products'=>'Related Products',
'Also_See'=>'Also See',
'Reviews'=>'Reviews',
'Continue_Shopping'=>'Continue Shopping',
'Update_Cart'=>'Update Cart',
'Cart'=>'Cart',
'Code_Expired'=>'Expired Code',
'Correct_Code'=>'Correct Code',
'Wrong_Code'=>'Wrong Code',
'Discount_Code'=>'Discount Code',
'Specification'=>'Specification',
'Product_Details'=>'Product Details',
'Add_to_Wishlist'=>'Add to Wishlist',
'QtyMoreAvQty'=>'Quantity More Than Avalaible',
'Sub_Total'=>'Sub Total',
'Cupon_Code'=>'Cupon Code',
'CuponCode'=>'Cupon Code',
'Grand_Total'=>'Grand Total',
'Checkout'=>'Checkout',
'My_Orders'=>'My Orders',
'Shipping'=>'Shipping',
'Processing'=>'Processing',
'Cash_on_Delivery'=>'Cash on Delivery',
'Place_Order'=>'Place Order',
'Your_Order'=>'Your Order',
'Order_Successfully'=>'Order Successfully',
'Other_Phone'=>'Other Phone',
'Billing_Details'=>'Billing Details',
'Your_Addresses'=>'Your  Addresses',
'User?'=>'RETURNING CUSTOMER?',
'ClickHereToLogin'=>'Click Here To Login',
'ShippingType'=>'Shipping Type',
'ShippingStatus'=>'Shipping Status',
'Center'=>'Center',
'Color'=>'Color',
'Weight'=>'Weight',
'Addresses'=>'Addresses',
'Address_Name'=>'Address Name',
'Backup'=>'Backup',
'DeropOff'=>'Derop Off',
'Client_Address'=>'Client Address',
'Able_to_Open'=>'Able to Open',
'Ship_Sort'=>'Shipping Sort',
'Requests'=>'Requests',
'Paid'=>'Paid',
'Order'=>'Order',
'Total_Canceled_Orders'=>'Total Canceled Orders',
'Total_Precent'=>'Total Precent',
'Canceld_Order'=>'Canceld Order',
'Delegate_Precent'=>'Delegate Precent',
'Total_Paid'=>'Total Paid',
'Total_Delegate_Precent'=>'Total Delegate Precent',
'Canceled_Order_Num'=>'Canceled Order Numbers',
'Cancel_Type'=>'Cancel Type',
'Shipping_Paid'=>'Shipping Paid',
'Shipping_Not_Paid'=>'Shipping Not Paid',
'Shipping_Company'=>'Shipping Company',
'Shipping_Delegate'=>'Shipping Delegate',
'Shipping_Precent'=>'Shipping Precent',
'Total_Exchange_Commision'=>'Total Exchange Commision',
'Total_Commision'=>'Total Commision',
'Precent_Do'=>'Precent ?',
'Orders_Num'=>'Order Numbers',
'Total_Orders'=>'Total Orders',
'ExchangeCommissions'=>'Exchange Commissions',
'ExchangeCommissionsSechdule'=>'Exchange Commissions Sechdule',
'Order_Num_Recived'=>'Orders Recived Number',
'Order_Num_Pending'=>'Orders Pending Number',
'Order_Total_Recived'=>'Total Order Recived',
'Order_Total_Pending'=>'Total Order Pending',
'Money_Client_Recived'=>'Money Client Recived',
'ReportMyOrders'=>'Report My Orders',
'AddNewClient'=>'Add New Client',
'AddNewVendor'=>'Add New Vendor',
'Not_Enough_Qty'=>'Not Enough Qty',
'Delegate_Commission'=>'Delegate Commission',
'Orders_Number'=>'Orders Number',
'Shipping_Order_Total'=>'Shipping Orders Total',
'Order_Total'=>'Orders Total',
'ReportOrders'=>'Report Orders',
'VendorCollections'=>'Vendor Collections',
'MyOrders'=>'My Orders',
'Number'=>'Number',
'Goods_Price'=>'Goods Price',
'Shipping_Price'=>'Shipping Price',
'Breakable'=>'Breakable',
'ShippingOrder'=>'Shipping Order',
'Town'=>'Town',
'Buliding_Num'=>'Buliding Number',
'Floor'=>'Floor',
'Flat_Num'=>'Flat Number',
'Special_Mark'=>'Special Mark',
'Street'=>'Street',                
'ShippingTransferRecived'=>'Shipping Transfer Recived',                
'CostShip'=>'Cost Shipping',                
'Tax_POS'=>'Change Tax  POS',                
'Total_Net'=>'Total Net',                
'Code_Typeee'=>'Code Type',                
'World_Code'=>'World Code',                
'Total_Bill'=>'Total Bill',                
'Device_TypeRequired'=>'Device Type Required',                
'CurrentAssetPrice'=>'Current Asset Price',                
'AlertStoresClientAccount'=>'Must Both of Stores have Client Account',                
'Show_Ship'=>'Show Shipping Company (Stores Transfer)',        
'Tax_Registration_Number'=>'Tax Registration Number',
'Tax_activity_code'=>'Tax Activity Code',
'work_nature'=>'Work Nature',
'Nationality'=>'Nationality',
'Buliding_Num'=>'Buliding Number',
'Postal_Code'=>'Postal Code',
'tax_magistrate'=>'Tax Magistrate',
'Client_ID'=>'Client ID',
'Serial_Client_ID'=>'Serial Client ID',
'Person'=>'Person',
'Trading_Data'=>'Trading Data',
'Egyptian_trading_company'=>'Egyptian Trading Company',
'foreign_trading_company'=>'Foreign Trading Company',    
'Bill_Electronic'=>'Bill Electronic',
'Send_Bill_Purchases'=>'Send Bill Purchases',
'Send_Bill_Sales'=>'Send Bill Sales',
'Bill_Purchases_Sent'=>'Bill Purchases Sent',
'Bill_Sales_Sent'=>'Bill Sales Sent',
'Hotels'=>'Hotels',
'Rooms'=>'Rooms',
'RoomCode'=>'Room Code',
'Bulding_Name'=>'Bulding Name',
'Adults_Num'=>'Adults Number',
'Childs_Num'=>'Childs Number',
'Beds_Num'=>'Beds Number',
'Sent_Successfully'=>'Sent Successfully',
'Reservations'=>'Reservations',
'u_not_Choiced_Any_Bill'=>'You not Choiced Any Bill',
'Reservations_Sechdule'=>'Reservations Sechdule',
'TotalDiscountPrint'=>'Total Discount',
'TotalTaxPrint'=>'Total Tax',
'ProductsNumber'=>'Products Number',
'TotalQtyPrint'=>'Total Qty',
'Credit'=>'Credit',
'Barcode'=>'Barcode',
'Taknet'=>'Taknet',
'Address'=>'Address',
'Phone1'=>'Phone 1',
'Phone2'=>'Phone 2',
'Phone3'=>'Phone 3',
'Phone4'=>'Phone 4',
'Text'=>'Text',
'Seal'=>'Seal',
'Room'=>'Room',
'RoomsType'=>'Rooms Type',
'ReservationsReport'=>'Reservations Report',
'Resrervation_Num'=>'Resrervation Numbers',
'Total_Reservation'=>'Total Reservation',  
'DelegateSalesDetails'=>'Delegate Sales Details',
'StoresSalesDetails'=>'Stores Sales Details',            
'TaxBill'=>'Tax Bill ?',            
'No_Tax'=>'Without Tax',            
'Yes_Tax'=>'With Tax',            
'TaxCode'=>'Tax Code',            
'PurchasesSechduleTax'=>'Purchases Sechdule Tax',            
'SalesSechduleTax'=>'Sales Sechdule Tax',            
'ProductProfits'=>'Product Profits',            
'Code_Report'=>'Barcode in Report',            
'Profit'=>'Profit',            
'Employment_levels'=>'Employment levels',            
'Insurance_companies'=>'Insurance Companies',            
'Employment_data'=>'Employment Data',            
'EmpSort'=>'Employee Sort',
'CV'=>'CV',
'ID_Image'=>'ID Image',
'Criminal_status'=>'Criminal Status',
'Contract'=>'Contract',
'health_certificate'=>'Health Certificate',
'Search_Card'=>'Search Card',
'Recruitment_certificate'=>'Recruitment Certificate',
'employee_profile'=>'Employee Profile',
'duration_criminal_investigation'=>'Duration of criminal investigation',
'Birthdate'=>'Birthdate',
'Attitude_recruiting'=>'Attitude Recruiting',
'Job_Number'=>'Job Number',
'date_resignation'=>'Date of Resignation',
'Living'=>'Living',
'Branch'=>'Branch',
'Level'=>'Level',
'Religion'=>'Religion',
'Insurance_salary'=>'Insurance Salary',
'Insurance_companies'=>'Insurance companies',
'Previous_experience'=>'Previous experience',            
'JobApplication'=>'Job Application',            
'JobRequestsSechdule'=>'Job Requests Sechdule',            
'TransferedSuccessfully'=>'Transfered Successfully',            
'Letter'=>'Letter',            
'ProfitPrecent'=>'Profit Precent',            
'MonthlyTarget'=>'Monthly Target',            
'QuarterTarget'=>'Quarter Year Target',            
'SemiTarget'=>'Semi Year Target',            
'YearlyTarget'=>'Yearly Target',            
'MyGoals'=>'My Goals',            
'PreviousMonth'=>'Previous Month',            
'ResignationRequest'=>'Resignation Request',            
'Disclaimer'=>'Disclaimer',            
'Accepted'=>'Accepted',            
'Refused'=>'Refused',            
'Resignation_Date'=>'Resignation Date',            
'ResignationRequestSechdule'=>'Resignation Request Sechdule',            
'SafeNotEnoughMoney'=>'Not Enough Money in Bank',
'Origin_Number'=>'Origin Number',
'Origin_Country'=>'Origin Country',
'SearchCode1'=>'Search Code 1',
'SearchCode2'=>'Search Code 2',
'Icon_Payment_Recipt'=>'Icon Payment Vouchers and Recipt Vouchers',
'SearchCode'=>'Search Code',
'Service_Fee'=>'Service Fee',
'Company_Precent'=>'Company Precent',
'InstallmentCompanies'=>'Installment Companies',
'Ratios'=>'Ratios',
'TaxOnTotal'=>'Tax On Total',
'TaxOnTotalType'=>'Tax Type',
'ProfitTax'=>'Profit Tax',
'InstallCompany'=>'Install Company',
'ContractNumber'=>'Contract Number',
'PayFees'=>'Pay Fees ? ',
'ServiceFee'=>'Service Fee',
'CompanyPrecent'=>'Company Precent',
'TotalServiceFee'=>'Total Service Fee',
'Sales_Bills'=>'Sales Bills Report',
'Return'=>'Return',
'Return_Purchase'=>'Return Purchase',
'Stores_Transfer'=>'Stores Transfer',
'Product_Code'=>'Product Code',
'Av_Qty'=>'Avaliable Qty',
'Total_BF_Tax'=>'Total Before Tax',
'ShowReport'=>'Show Report',
'ChoicedAll'=>'Choiced All',
'CancelChoicedAll'=>'Cancel Choiced All',
'OppositeChoiced'=>'Opposite Choiced',
'DefaultChoiced'=>'Default Choiced',
'ShiftCode'=>'Shift Code',
'Bills_Nums'=>'Bills Numbers',
'Total_Bills'=>'Total Bills',
'PrintDetails'=>'Print Details',
'Edit_Transfer'=>'Edit Transfer',
'FromSafe'=>'From Safe',
'ToSafe'=>'To Safe',
'FromStores'=>'From Stores',
'ToStores'=>'To Stores',
'OldAmount'=>'Old Amount',
'Ship'=>'Company Shipping',
'SaveasDefault'=>'Save as Default',
'ChoiceColumn'=>'Choice Columns want to show in report',
'ChoiceColumnDetails'=>'Choice Columns want to show in report details',
'Return_Maintance'=>'Return Maintance',
'Incom_Manufacturing'=>'Incom Manufacturing',
'Outcom_Manufacturing'=>'Outcom Manufacturing',
'StoresTransferFrom'=>'Stores Transfer From',
'StoresTransferTo'=>'Stores Transfer To',        
'SalePrice'=>'Sale Price',        
'Sales_Qty'=>'Sales Qty',   
'StoresBalancesTwo'=>'Store Balances Moves',   
'CreditStores'=>'Credit Stores',   
'ItemCost'=>'Item Cost',   
'SubType'=>'Sub Type',   
'Expenses'=>'Expenses',   
'Inside'=>'Inside',   
'Outside'=>'Outside',   
'SalesTakeGoods'=>'Sales Take Goods',   
'ReportGroup1'=>'Report Group 1',   
'ReportGroup2'=>'Report Group 2',   
'ReportGroup3'=>'Report Group 3',   
'ReportGroup4'=>'Report Group 4',   
'ReSend'=>'ReSend',   
'Name_Width'=>'Name Width',   
'Print_Text_Footer_Manufacturing'=>'Print Text Footer Manufacturing',   
'TaxBill'=>'Tax Bill',   
'TotalExpensesSafes'=>'Total Expenses Safes',   
'InstallmentCompaniesSales'=>'Installment Companies Sales',   
'StoresCosts'=>'Stores Costs',   
'ProfitDelegateSalesDetails'=>'Profit Delegate Sales Details',   
'Bill_Sales_Sent_Web'=>'Bills Sent Web',   
'Return_Sales_TaxSechdule'=>'Return Sales Tax Sechdule',   
'Send_Bill_ReturnSales'=>'Send Bill Return Sales',   
'Bill_ReturnSales_Sent'=>'Bill Return Sales Sent',   
'CustomersGroup'=>'Customers Group',   
'Total_Workmanship_Price'=>'Total Workmanship Price',   
'ExecutingReceivingSecretariat'=>'Executing Receiving Secretariat',   
'Workmanship_Price'=>'Workmanship Price',   
'SalesProsMoreDetails'=>'Sales Products Details',   
'SalesCustomersGroups'=>'Sales Customers Groups in Details',   
'ManufacturingModelSecretariat'=>'Manufacturing Model Secretariat',   
'ManufacturingModelSecretariatPrecent'=>'Manufacturing Model Secretariat Precent',   
'ManufacturingModelSecretariatSechdule'=>'Manufacturing Model Secretariat Sechdule',   
'Total_Sales_Maintance'=>'Total Sales Maintance',   
'Total_Return_Maintance'=>'Total Return Maintance',   
'StoresTransferProfit'=>'Stores Transfer Profit',   
'MaintainceBillTalf'=>'Maintaince Bill Consists',   
'Change_Way_Stores_Transfer'=>'Control of Stores Transfers Way',   
'Type_Transfer'=>'Type Transfer',   
'Cost_Store'=>'Cost Store',   
'Sale_Price'=>'Sale Price',   
'Cost_Price'=>'Cost Price',   
'Dashboard'=>'Dashboard',   
'Salaries'=>'Salaries',   
'Success'=>'Success',    
'Close_Shift_Successfully'=>'Close Shift Successfully',    
'SuccessPass'=>'Success Password',    
'WrongPass'=>'Wrong Password',    
'POS_Qty'=>'POS Qty',   
'POS_Barcode'=>'POS Barcode',   
'MaintanceSalesReport'=>'Maintance Sales Report',   
'Maintenance_Tune'=>'Maintenance Tune',   
'Customer_Debts'=>'Customer Debts',   
'Vendor_Debts'=>'Vendor Debts',   
'Fixed_Assets_Report'=>'Fixed Assets Report',   
'Allowances'=>'Allowances',   
'BrandsSales'=>'Brands Sales',   
'ReturnSales'=>'Return Sales',   
'ReciptVoucher'=>'Recipt Voucher',   
'SafeTransfer'=>'Safe Transfer',   
'PaymentVoucher'=>'Payment Voucher',   
'ReturnPurchases'=>'Return Purchases',   
'Total_Debaitor'=>'Total Debaitor',   
'GUESTLIST'=>'GUEST LIST Store',   
'Total_Excess_Price'=>'Total Excess Price',   
            
'Discounts'=>'Discounts',   
'Return_Sale_Taransfer_Sure'=>'Return Sale Stores Taransfer',   
'Stores_Sales_Transfers_Sechdule'=>'Stores Sales Transfers Sechdule',   
'Total_Price_Return_Qty'=>'Total Price Return Qty',   
'Mobile'=>'Mobile',   
'Website'=>'Website',     
'Sure_Recipts'=>'Sure Recipts',     
'Sure_Opening_Entries'=>'Sure Opening Entries',     
'Sure_Payment_Voucher'=>'Sure Payment Voucher',     
'Sure_Journalizing'=>'Sure Journalizing',     
'Sure_Receipt_Voucher'=>'Sure Receipt Voucher',     
'Changed_Price_Successfully'=>'Changed Price Successfully',     
'POS_RecivedDate'=>'POS Recived Date',     
'Monthly'=>'Monthly',     
'3M'=>'3 Month',     
'6M'=>'6 Month',     
'Yearly'=>'Yearly',     
'CommentsClients'=>'Comments Clients',     
'Warranty'=>'Warranty',     
'RecivedDate'=>'Recived Date',     
'Executing_Qty'=>'Executing and Reciving Qty',     
'AlertAccounting'=>'Please do not add employees, customers or suppliers from the Accounting Manual',     
           
'Cost_Sales'=>'Cost Sales',            
'Profit_Sales'=>'Profit Sales',            
            



            
            
'Search_Type'=>'Search Type',        
'Sales_Count'=>'Sales Count',        
'ProductPrice'=>'Original Product Price',        
'TotSalePrice'=>'Total Sales Price',        
'TotProductPrice'=>'Total Original Product Price',        
'Debitor_Coin'=>'Debitor Coin',
'Creditor_Coin'=>'Creditor Coin',
'Recipt_Voucher'=>'Recipt Voucher',      
'VendorAccountStatementColumn'=>'Vendor Account Statement Column Details',      
'ClientsStatement'=>'Clients Statement',
'VendorsStatement'=>'Vendors  Statement',
'Sub_Account'=>'Sub Account',
'Total_Debiator'=>'Total Debiator',
'Total_Sales_Cost'=>'Total Sales Cost',
'Total_Profit'=>'Total Profit',
'Total_Payment_Voucher'=>'Total Payment Voucher',
'Profit_Net'=>'Profit Net',
'StorePriceList'=>'Store Price List',
'IDExpireDate'=>'ID Expire Date',
'LicensExpireDate'=>'Licens Expire Date',
'PassportExpireDate'=>'Passport Expire Date',
'InventorySerial'=>'Inventory Serial',
'CodeTax'=>'Code Tax',
'ProductWIthStartRepeat'=>'Product WIth Start Period Repeat',
'Version_Type'=>'Version Type',
'Invoice_Type'=>'Invoice Type',
'Real'=>'Real',
'Experimental'=>'Experimental',
'Computer_SN'=>'Computer Serial Number',
'Landmark'=>'Landmark',
'Add_Info'=>'Addation Info',
'Space'=>'Space',
'Storage'=>'Storage',
'Processor'=>'Processor',
'Camera'=>'Camera',
'Screen'=>'Screen',
'OS'=>'Operating System',
'Battery'=>'Battery',
'Warranty'=>'Warranty',
'Color'=>'Color',
'Category'=>'Category',
'Model'=>'Model',
'OtherStoresQty'=>'Other Stores Qty',
'Choiced_Stores'=>'Choiced Stores',
'Stores_Qty'=>'Stores Qty',
'Total_Balance'=>'Total Balance',
'StoresBalancesCat'=>'Stores Balances Categories',
'TotalAchieveProfit'=>'Total Achieve Profit',
'TotalProfitRequested'=>'Total Profit Requested',
'ProfitDif'=>'Profit Differnce',
'DelegateSalesDetails'=>'Delegate Sales Details',
'DailyClosingDetails'=>'Daily Closing Details',
'StoresTarnsfer'=>'Stores Tarnsfer After Sure',
'DelegateEmp'=>'Delegate during Employee',
'DeleteMoves'=>'Delete Moves',
'DiscountTaxShow'=>'Discount Tax Show',
'DiscountTax'=>'Discount Tax',
'Attendence_Policy'=>'Attendence Policy',
'Departure_Policy'=>'Departure Policy',
'DiscountLate'=>'Discount Late',
'DiscountDeparture'=>'Discount Departure',
'HR_Reports'=>'HR Reports',
'Attendence_Late'=>'Attendence Late',
'Departure_Early'=>'Departure Early',
'Actual_Time'=>'Actual Time',
'Hour_Value'=>'Hour Value',
'PaySalaryReport'=>'Pay Salary Report',
'Hours_Numberss'=>'Hours Numbers',
'Financial_Value'=>'Financial Value',
'AttendenceValueReport'=>'Attendence Value Report',
'AttendenceAndDepartureReport'=>'Attendence And Departure Report',
'Tickets'=>'Tickets',
'Sender_Address'=>'Sender Address',
'Sender_Phone'=>'Sender Phone',
'Addressees_Address'=>'Addressees Address',
'Addressees_Phone'=>'Addressees Phone',
'Sender_Name'=>'Sender Name',
'Addressees_Name'=>'Addressees Name',
'ticket_price'=>'ticket price',
'ticket_discount'=>'ticket discount',
'TicketsSechdule'=>'Tickets Sechdule',
'ShippingList'=>'Shipping List',
'Ticket_Store'=>'Ticket Store',
'Driver'=>'Driver',
'Car_Store'=>'Car Store',
'Travel_Area'=>'Travel Area',
'Access_Area'=>'Access Area',
'Date_Travel'=>'Date Travel',
'Date_Arrival'=>'Date Arrival',
'Car_Number'=>'Car Number',
'Total_Cash'=>'Total Cash',
'Total_Later'=>'Total Later',
'Tickets_Numbers'=>'Tickets Numbers',
'ShippingListSechdule'=>'Shipping List Sechdule',
'ShipmentReceipts'=>'Shipment Receipts',
'Recived_Store'=>'Recived Store',
'QRـPrinting'=>'QR Printing',
'TicketsClient'=>'Tickets',
'Visit_Cost'=>'Visit Cost',
'Customerـfollowـup'=>'Customer follo up',
'Shipping_Num'=>'Shipping Number',
'ShipmentReceiptsSechdule'=>'Shipment Receipts Sechdule',
'ShipmentReceiptsClients'=>'Shipment Receipts Clients',
'ShipmentReceipts_Num'=>'Shipment Receipts Number',
'ShipmentReceiptsClientsSechdule'=>'Shipment Receipts Clients Sechdule',
'Show_File_ReciptVoucher'=>'Show File Recipt Voucher',
'Show_File_PaymentVoucher'=>'Show File Payment Voucher',
'Show_File_Sales'=>'Show File Sales',
'Show_File_Purchases'=>'Show File Purchases',
'Show_File_Checks'=>'Show File Checks',
'Show_File_InsurancePaper'=>'Show File Insurance Paper',
'Show_File_TransferStores'=>'Show File Transfer Stores',
'passwordsame'=>'Confirm Password not Same Password',
'Account_Balance'=>'Account Balance',
'allow_discount'=>'Allow Discount',
'net_sales'=>'Net Sales',
'ImportingChecks'=>'Importing Checks',
'minus_settlement'=>'Minus Settlement',
'shipping_ticket'=>'Shipping Ticket',
'total_received'=>'Total Received',
'net_purchases'=>'Net Purchases',
'Borrows'=>'Borrows',
'Loans'=>'Loans',
'Excnanges'=>'Excnanges',
'plus_settlement'=>'Plus Settlement',
'return_maintaince'=>'Return Maintaince',  
'total_issued'=>'total issued',
'total_daily_movements_in_safe'=>'total daily movements in safe',
'actual_balance_in_safe'=>'actual balance in safe',
'Sales_Petrol'=>'Sales Petrol',         
'fixed_assets'=>'fixed assets',         
'traded_assets'=>'traded assets',         
'other_assets'=>'other assets',         
'short_liabilities'=>'short liabilities',         
'From_Ar_Name'=>'From Arabic Name',         
'To_Ar_Name'=>'To Arabic Name',         
'From_En_Name'=>'From English Name',         
'To_En_Name'=>'To English Name',         
'Recipients_Name'=>'Recipients Name',         
'I_received'=>'I received',         
'Resident'=>'Resident',         
'Center'=>'Center',         
'ID_Number'=>'ID Number',         
'Personal_family'=>'Personal/Family',         
'Coming_From'=>'Coming From',         
'Dately'=>'Date',         
'close'=>'Close',         
'From_Mr'=>'From Mr',         
'Amount_Of'=>'Amount Of',         
'Just'=>'Just',         
'Commercial'=>'Commercial Data',         
'Insurance_Letter2'=>'And if I do not deliver this amount, I will be wasted and betrayed the trust, and I bear the criminal responsibility resulting from that, and I will not be discharged except by receiving this receipt.',         
'Insurance_Letter3'=>'This is my receipt',         
'Recipients'=>'Recipients',         
'Company_Arabic_Name'=>'Company Arabic Name',         
'Company_English_Name'=>'Company English Name',         
'Insurance_Letter'=>'As a matter of trust, to deliver it and hand over the amount to Mr', 
'Arabic_Print_Text'=>'Arabic Print Text',         
'English_Print_Text'=>'English Print Text',         
'Arabic_Print_Text_Footer'=>'Arabic Print Text Footer',         
'English_Print_Text_Footer'=>'English Print Text Footer',         
'Arabic_Print_Text_Footer_Manufacturing'=>'Arabic Print Text Footer Manufacturing',         
'English_Print_Text_Footer_Manufacturing'=>'English Print Text Footer Manufacturing',         
'Arabic_Print_Text_Footer_Sales'=>'Arabic Print_Text Footer Sales',         
'English_Print_Text_Footer_Sales'=>'English Print Text Footer Sales',         
'Arabic_Print_Text_Footer_Quote'=>'Arabic Print Text Footer Quote',         
'English_Print_Text_Footer_Quote'=>'English Print Text Footer Quote',         
'Arabic_Print_Text_Footer_Secretariat'=>'Arabic Print Text Footer Secretariat',         
'English_Print_Text_Footer_Secretariat'=>'English Print Text Footer Secretariat',         
'Arabic_Name_Sales_Bill'=>'Arabic Name Sales Bill',         
'English_Name_Sales_Bill'=>'English Name Sales Bill',         
'Arabic_Name_Sales_Order_Bill'=>'Arabic Name Sales Order Bill',         
'English_Name_Sales_Order_Bill'=>'English Name Sales Order Bill',         
'Arabic_Name_Quote_Bill'=>'Arabic Name Quote Bill',         
'English_Name_Quote_Bill'=>'English Name Quote Bill',         
'Vendor_Collection'=>'Vendor Collection',         
'Client_Collection'=>'Client Collection',         
'Sure_Order'=>'Sure Order',           
'Without_Dele_Precent'=>'Without the delegate ratio',    
'Total_Sales'=>'Total Sales',           
'Return_Sales'=>'Return Sales',           
'Total_return_Sales'=>'Total Return Sales',           
'Discount_Allow'=>'Discount Allow',           
'Net_Sales'=>'Net Sales',     
'Recipt_Vouchers'=>'Recipt Vouchers',           
'Total_Recipt_Vouchers'=>'Total Recipt Vouchers',           
'Payment_Vouchers'=>'Payment Vouchers',           
'Total_Payment_Vouchers'=>'Total Payment Vouchers',           
'Safe_Value'=>'Safe Value',           
'Return_Purchases'=>'Return Purchases',           
'Total_Return_Purchases'=>'Total Return Purchases',           
'Shift_Code'=>'Shift Code',           
'Maintaince_Bill'=>'Maintaince Bill',           
'Return_Maintaince'=>'Return Maintaince',   
'SalesOrderType'=>'Sales Order Type',   
'ECommercceSaleType'=>'ECommercce Sale Type',   
'Company_Address_Arabic'=>'Company Address Arabic',   
'Company_Address_English'=>'Company Address English',   
'Get_in_Touch'=>'Get in Touch',   
'Info'=>'Info',   
'Already_have_an_account'=>'Already have an account ?',   
'Dont_have_an_account'=>'Dont have an account ?',   
'Shop_Now'=>'Shop Now',   
'Description'=>'Description',   
'Add_New_Address'=>'Add New Address',   
'Leave_us_a_Message'=>'Leave us a Message',   
'Resturant'=>'Resturant',   
'Tables'=>'Tables',   
'Kitchen_Order'=>'Kitchen Order',   
'Variable_Aggregate'=>'Variable Aggregate',   
'Additions'=>'Additions',   
'Printer'=>'Printer',   
'Table_Number'=>'Table Number',   
'Place_Ar'=>'Place in Arabic',   
'Place_En'=>'Place in English',   
'Chairs_Num'=>'Chairs Number',   
'QR'=>'QR',   
'ResturantSales'=>'Resturant Sales',   
            
'Safes_Transfer'=>'Safes Transfer',           
'Total_Safes_Transfer'=>'Total Safes Transfer',           
'Exporting_Checks'=>'Exporting Checks',           
'Total_Exporting_Checks'=>'Total Exporting Checks',           
'Disability_Settlement'=>'Disability Settlement',           
'Total_Disability_Settlement'=>'Total Disability Settlement',           
'Insurance_Paper'=>'Insurance Paper',           
'Total_Insurance_Paper'=>'Total Insurance Paper',           
'Sales_Petrol'=>'Sales Petrol',           
'Total_Sales_Petrol'=>'Total Sales Petrol',  
'Shipping_Ticket'=>'Shipping Ticket',           
'Total_Shipping_Ticket'=>'Total Shipping Ticket',           
'Maintaince'=>'Maintaince',           
'Total_Maintaince'=>'Total Maintaince',           
'Total_Incom'=>'Total Incom',           
'Net_Purchases'=>'Net Purchases',           
'Incoming_Checks'=>'Incoming Checks',           
'Total_Incoming_Checks'=>'Total Incoming Checks',           
'Increment_Settlement'=>'Increment Settlement',           
'Total_Increment_Settlement'=>'Total Increment Settlement',   
'Total_Return_Maintaince'=>'Total Return Maintaince',           
'Salaries'=>'Salaries',           
'Total_Salaries'=>'Total Salaries',           
'Borrows'=>'Borrows',           
'Total_Borrows'=>'Total Borrows',           
'Loans'=>'Loans',           
'Total_Loans'=>'Total Loans',           
'Exchanges'=>'Exchanges',           
'Total_Exchanges'=>'Total Exchanges',           
'Total_Export'=>'Total Export',           
'total_daily_transactions_in_safe'=>'total daily transactions in safe',           
'actual_balance_in_safe'=>'actual balance in safe',           
'Filter'=>'Filter',           
'Previous_Balance'=>'Previous Balance',           
'Cost_Per_One'=>'Cost Per One',           
'Manfacturing'=>'Manfacturing',           
'Checks'=>'Checks',           
'Not_Hold_Qties'=>'Not Hold Qties',           
'Hold_Qties'=>'Hold Qties',           
'Columns'=>'Columns',           
'SalaryPayed'=>'Salary Payed Report',           
'Credit_Period'=>'Credit Period',           
'CantDeleteAnyItemHasTraffic'=>'Cant Delete Any Item Has Traffic',           
'Waiter'=>'Waiter',           
'End'=>'End',           
'AddNewTable'=>'Add New Table',           
'Table'=>'Table',           
'NewTable'=>'New Table',           
'Tax_Service_Type'=>'Tax Service Type',           
'Tax_Service'=>'Tax Service',           
'MoveToTable'=>'Move To Table',           
'MainTable'=>'Main Table',           
'Request_Send'=>'Request Send',           
'RUSWRS'=>'Are You Sure want to Request Send',           
'KitchenBill'=>'Kitchen Bill',           
'SalesBill'=>'Sales Bill',           
'Ended'=>'Ended',           
'ResturantMenu'=>'Resturant Menu',           
'Merge_Tables'=>'Merge Tables',           
'Separating_Tables'=>'Separating Tables',           
'Moving_Tables'=>'Moving Tables',           
'Reserved_Tables'=>'Reserved Tables',           
'KitchenScreen'=>'Kitchen Screen',           
'RecivedScreen'=>'Recived Screen',           
'Hall_Service_Type'=>'Hall Service Type',           
'Hall_Service_Precent'=>'Hall Service Precent',           
'ResturantOrderType'=>'Resturant Order Type',           
'Reviews'=>'Reviews',           
'Arrange'=>'Arrange',           
'Persons'=>'Persons',           
'Budget'=>'Budget',           
'Visa2'=>'POS Machine',           
'Briefs'=>'Briefs',           
'MonthlySales'=>'Monthly Sales',           
'WeeklySales'=>'Weekly Sales',           
'HomeMainScreen'=>'Home Main Screen',           
'Images'=>'Images',           
'SalesQuarters'=>'Sales Quarters of Year',           
'SalesHalfs'=>'Sales Halfs of Year',           
'YearlySales'=>'Yearly Sales',           
'BG_Image'=>'BG Image',           
'Title_Image'=>'Title Image',           
'Video_Link'=>'Video Link',                   
'VideoSection'=>'Video Section',           
'Reservations'=>'Reservations',           
'ResturantStyle'=>'Resturant Style', 
'Image_1'=>'Image 1',           
'Image_4'=>'Image 4',           
'Number_1'=>'Number 1',           
'Number_2'=>'Number 2',           
'Number_3'=>'Number 3',           
'Number_4'=>'Number 4',           
'Arabic_Title_1'=>'Arabic Title 1',           
'Arabic_Title_2'=>'Arabic Title 2',           
'Arabic_Title_3'=>'Arabic Title 3',           
'Arabic_Title_4'=>'Arabic Title 4',           
'English_Title_1'=>'English Title 1',           
'English_Title_2'=>'English Title 2',           
'English_Title_3'=>'English Title 3',           
'English_Title_4'=>'English Title 4',           
'Menu'=>'Menu',           
'Privacy'=>'Privacy & Policy',           
'PDF_Menu'=>'PDF Menu',           
'ReadMore'=>'Read More',           
'Book'=>'Book Now',           
'Recipt'=>'Recipt ',           
'CoinResturantWebsite'=>'Coin Resturant Website ',           
'BlogDetails'=>'Blog Details ',           
'UnApprove'=>'UnApprove ',           
'Arabic_Sub_Title'=>'Arabic Sub Title ',           
'English_Sub_Title'=>'English Sub Title ',           
'Order_Summary'=>'Order Summary ',           
'MustBeEmp'=>'Must Be Employee',           
'Briefs'=>'Briefs',                  
'Total_Sales_Today'=>'Total Sales Today',           
'Statistics_Total'=>'Statistics Total',           
'Statistics_Graph'=>'Statistics Graph',           
'Total_Sales_This_Month'=>'Total Sales This Month',           
'Total_Sales_This_Year'=>'Total Sales This Year',           
'Total_Sales_Last_3_Month'=>'Total Sales Last 3 Month',           
'TotalSalesFirstQuarter'=>'Total Sales First Quarter of Year',           
'TotalSalesSecondQuarter'=>'Total Sales Second Quarter of Year',           
'TotalSalesThirdQuarter'=>'Total Sales Third Quarter of Year',           
'TotalSalesFourthQuarter'=>'Total Sales Fourth Quarter of Year',    
'TotalSalesFirstHalf'=>'Total Sales First Half of Year',           
'TotalSalesSecondHalf'=>'Total Sales Second Half of Year',           
'TotalSalesTodayCash'=>'Total Sales Today Cash',           
'TotalSalesTodayLater'=>'Total Sales Today Later',           
'Total_Purchases_Today'=>'Total Purchases Today',               
'Total_Purchases_This_Month'=>'Total Purchases This Month',           
'Total_Purchases_This_Year'=>'Total Purchases This Year',           
'Total_Purchases_Last_3_Month'=>'Total Purchases Last 3 Month',           
'TotalPurchasesFirstQuarter'=>'Total Purchases First Quarter of Year',           
'TotalPurchasesSecondQuarter'=>'Total Purchases Second Quarter of Year',           
'TotalPurchasesThirdQuarter'=>'Total Purchases Third Quarter of Year',           
'TotalPurchasesFourthQuarter'=>'Total Purchases Fourth Quarter of Year',    
'TotalPurchasesFirstHalf'=>'Total Purchases First Half of Year',           
'TotalPurchasesSecondHalf'=>'Total Purchases Second Half of Year',           
'TotalPurchasesTodayCash'=>'Total Purchases Today Cash',           
'TotalPurchasesTodayLater'=>'Total Purchases Today Later',             
'Total_ReturnSales_Today'=>'Total Return Sales Today',               
'Total_ReturnSales_This_Month'=>'Total Return Sales This Month',           
'Total_ReturnSales_This_Year'=>'Total Return Sales This Year',           
'Total_ReturnSales_Last_3_Month'=>'Total Return Sales Last 3 Month',           
'TotalReturnSalesFirstQuarter'=>'Total Return Sales First Quarter of Year',           
'TotalReturnSalesSecondQuarter'=>'Total Return Sales Second Quarter of Year',           
'TotalReturnSalesThirdQuarter'=>'Total Return Sales Third Quarter of Year',           
'TotalReturnSalesFourthQuarter'=>'Total Return Sales Fourth Quarter of Year',    
'TotalReturnSalesFirstHalf'=>'Total Return Sales First Half of Year',           
'TotalReturnSalesSecondHalf'=>'Total Return Sales Second Half of Year',           
'TotalReturnSalesTodayCash'=>'Total Return Sales Today Cash',           
'TotalReturnSalesTodayLater'=>'Total Return Sales Today Later', 
'Vouchers'=>'Vouchers',     
'PaymentVoucherToday'=>'Total Payment Voucher Today', 
'PaymentVoucherMonthly'=>'Total Payment Voucher Monthly', 
'PaymentVoucherYearly'=>'Total Payment Voucher Yearly', 
'PaymentVoucherLast3Month'=>'Total Payment Voucher Last 3 Month', 
'PaymentVoucherQ1'=>'Total Payment Voucher Quarter one of Year', 
'PaymentVoucherQ2'=>'Total Payment Voucher Quarter two of Year', 
'PaymentVoucherQ3'=>'Total Payment Voucher Quarter three of Year', 
'PaymentVoucherQ4'=>'Total Payment Voucher Quarter four of Year', 
'PaymentVoucherH1'=>'Total Payment Voucher First Half of Year', 
'PaymentVoucherH2'=>'Total Payment Voucher Second Half of Year', 
'ReciptVoucherToday'=>'Total Recipt Voucher Today', 
'ReciptVoucherMonthly'=>'Total Recipt Voucher Monthly', 
'ReciptVoucherYearly'=>'Total Recipt Voucher Yearly', 
'ReciptVoucherLast3Month'=>'Total Recipt Voucher Last 3 Month', 
'ReciptVoucherQ1'=>'Total Recipt Voucher Quarter one of Year', 
'ReciptVoucherQ2'=>'Total Recipt Voucher Quarter two of Year', 
'ReciptVoucherQ3'=>'Total Recipt Voucher Quarter three of Year', 
'ReciptVoucherQ4'=>'Total Recipt Voucher Quarter four of Year', 
'ReciptVoucherH1'=>'Total Recipt Voucher First Half of Year', 
'ReciptVoucherH2'=>'Total Recipt Voucher Second Half of Year',       
'Total_InsurancePaper_Today'=>'Total Insurance Paper Today',  
'Total_InsurancePaper_This_Month'=>'Total Insurance Paper This Month',           
'Total_InsurancePaper_This_Year'=>'Total Insurance Paper This Year',           
'Total_InsurancePaper_Last_3_Month'=>'Total Insurance Paper Last 3 Month',           
'TotalInsurancePaperFirstQuarter'=>'Total Insurance Paper First Quarter of Year',           
'TotalInsurancePaperSecondQuarter'=>'Total Insurance Paper Second Quarter of Year',           
'TotalInsurancePaperThirdQuarter'=>'Total Insurance Paper Third Quarter of Year',           
'TotalInsurancePaperFourthQuarter'=>'Total Insurance Paper Fourth Quarter of Year',    
'TotalInsurancePaperFirstHalf'=>'Total Insurance Paper First Half of Year',           
'TotalInsurancePaperSecondHalf'=>'Total Insurance Paper Second Half of Year',                        
'TotalInsurancePaperCollected'=>'Total Insurance Paper Collected',
'Total_IncomChecks_Today'=>'Total Incom Checks Today',  
'Total_IncomChecks_This_Month'=>'Total Incom Checks This Month',           
'Total_IncomChecks_This_Year'=>'Total Incom Checks This Year',           
'Total_IncomChecks_Last_3_Month'=>'Total Incom Checks Last 3 Month',           
'TotalIncomChecksFirstQuarter'=>'Total Incom Checks First Quarter of Year',           
'TotalIncomChecksSecondQuarter'=>'Total Incom Checks Second Quarter of Year',           
'TotalIncomChecksThirdQuarter'=>'Total Incom Checks Third Quarter of Year',           
'TotalIncomChecksFourthQuarter'=>'Total Incom Checks Fourth Quarter of Year',    
'TotalIncomChecksFirstHalf'=>'Total Incom Checks First Half of Year',           
'TotalIncomChecksSecondHalf'=>'Total Incom Checks Second Half of Year',                        
'TotalIncomChecksCollected'=>'Total Incom Checks Collected',  
'Total_ExportChecks_Today'=>'Total Export Checks Today',  
'Total_ExportChecks_This_Month'=>'Total Export Checks This Month',           
'Total_ExportChecks_This_Year'=>'Total Export Checks This Year',           
'Total_ExportChecks_Last_3_Month'=>'Total Export Checks Last 3 Month',           
'TotalExportChecksFirstQuarter'=>'Total Export Checks First Quarter of Year',           
'TotalExportChecksSecondQuarter'=>'Total Export Checks Second Quarter of Year',           
'TotalExportChecksThirdQuarter'=>'Total Export Checks Third Quarter of Year',           
'TotalExportChecksFourthQuarter'=>'Total Export Checks Fourth Quarter of Year',    
'TotalExportChecksFirstHalf'=>'Total Export Checks First Half of Year',           
'TotalExportChecksSecondHalf'=>'Total Export Checks Second Half of Year',                        
'TotalExportChecksCollected'=>'Total Export Checks Collected',             
'TotalStoreCost'=>'Total Stores Cost',             
'TotalStoreQty'=>'Total Stores Qty',             
'Total_ReturnPurch_Today'=>'Total Return Purchases Today',               
'Total_ReturnPurch_This_Month'=>'Total Return Purchases This Month',           
'Total_ReturnPurch_This_Year'=>'Total Return Purchases This Year',           
'Total_ReturnPurch_Last_3_Month'=>'Total Return Purchases Last 3 Month',           
'TotalReturnPurchFirstQuarter'=>'Total Return Purchases First Quarter of Year',           
'TotalReturnPurchSecondQuarter'=>'Total Return Purchases Second Quarter of Year',           
'TotalReturnPurchThirdQuarter'=>'Total Return Purchases Third Quarter of Year',           
'TotalReturnPurchFourthQuarter'=>'Total Return Purchases Fourth Quarter of Year',    
'TotalReturnPurchFirstHalf'=>'Total Return Purchases First Half of Year',           
'TotalReturnPurchSecondHalf'=>'Total Return Purchases Second Half of Year',           
'TotalReturnPurchTodayCash'=>'Total Return Purchases Today Cash',           
'TotalReturnPurchTodayLater'=>'Total Return Purchases Today Later',                  
'Total_Expenses_Today'=>'Total Expenses Today',   
'Total_Expenses_This_Month'=>'Total Expenses This Month',           
'Total_Expenses_This_Year'=>'Total Expenses This Year',           
'Total_Expenses_Last_3_Month'=>'Total Expenses Last 3 Month',           
'TotalExpensesFirstQuarter'=>'Total Expenses First Quarter of Year',           
'TotalExpensesSecondQuarter'=>'Total Expenses Second Quarter of Year',           
'TotalExpensesThirdQuarter'=>'Total Expenses Third Quarter of Year',           
'TotalExpensesFourthQuarter'=>'Total Expenses Fourth Quarter of Year',    
'TotalExpensesFirstHalf'=>'Total Expenses First Half of Year',           
'TotalExpensesSecondHalf'=>'Total Expenses Second Half of Year',                
'Others'=>'Others',                 
'ClientsBalance'=>'Clients Balance',                 
'VendorsBalance'=>'Vendors Balance',                 
'Total_Safes'=>'Total Safes',                 
'pyramidal'=>'pyramidal',                 
'graph'=>'graph',                 
'MonthlyPurchases'=>'Monthly Purchases',                 
'WeeklyPurchases'=>'Weekly Purchases',                 
'YearlyPurchases'=>'Yearly Purchases',                 
'PurchasesQuarters'=>'Purchases Quarters',                 
'PurchasesHalfs'=>'Purchases Halfs',     
'ReturnSalesMonthly'=>'Return Sales Monthly',                 
'ReturnSalesWeekly'=>'Return Sales Weekly',                 
'ReturnSalesYearly'=>'Return Sales Yearly',                 
'ReturnSalesQuarters'=>'Return Sales Quarters of Year',                 
'ReturnSalesHalfs'=>'Return Sales Halfs of Year',                 
'ReturnPurchMonthly'=>'Return Purchases Monthly',                 
'ReturnPurchWeekly'=>'Return Purchases Weekly',                 
'ReturnPurchYearly'=>'Return Purchases Yearly',                 
'ReturnPurchQuarters'=>'Return Purchases Quarters of Year',                 
'ReturnPurchHalfs'=>'Return Purchases Halfs of Year',  
'ExpensesMonthly'=>'Expenses Monthly',                
'ExpensesWeekly'=>'Expenses Weekly',                
'ExpensesYearly'=>'Expenses Yearly',                
'ExpensesQuarters'=>'Expenses Quarters of Year',                
'ExpensesHalfs'=>'Expenses Halfs of Year',     
'Show_Other_Store'=>'Show Other Store',                
'Arabic_Brief_Desc'=>'Arabic Brief Desc',                
'English_Brief_Desc'=>'English Brief Desc',                
'VouchersMonthly'=>'Vouchers Monthly',                
'PaymentWeekly'=>'Payment Weekly',                
'ReciptWeekly'=>'Recipt Weekly',                
'ChecksMonthly'=>'Checks Monthly',                
'IncomChecksWeekly'=>'Incom Checks Weekly',                
'ExportChecksWeekly'=>'Export Checks Weekly',                
'Loose_Profit'=>'Loose and Profit',                
'GBSales'=>'Group and Brand Sales',                
'GroupSales'=>'Group Sales',                
'BrandSales'=>'Brand Sales',                
'AddPackage'=>'Add Package',                
'Packages'=>'Packages',                
'IncomListReport'=>'Incom List Report',                
'Guess_Price'=>'Guess Price',                
'Brief_Desc'=>'Brief Desc',                
'Offer_Start_Date'=>'Offer Start Date',                
'Offer_End_Date'=>'Offer End Date',                
'Renew_Date_Soon'=>'Dear user, the expiration date of your subscription is approaching. Please pay to ensure the continuity of the service a day before.',                
'Supply_Chain'=>'Supply Chain',
'Purchases_P'=>'Purchases',
'HighestDiscountRate'=>'Highest Discount Rate',
'StorePriceListt'=>'Store Qty List',
'SupplyChainReports'=>'Supply Chain Reports',
'Refuse_Quality'=>'Refuse Quality',
'Calendar'=>'Schedule',
'Safe_Transfer_Need_Confirmation'=>'Safe Transfer Need Confirmation',
'ConfirmationSafeTransfer'=>'Confirmation Safe Transfer',
'Journalizings_Need_Confirmation'=>'Journalizings Need Confirmation',
'Journalizings_Confirmed'=>'Journalizings Confirmed',            
'Receipt_Voucher_Need_Confirmation'=>'Receipt Voucher Need Confirmation',
'Receipt_Voucher_Confirmed'=>'Receipt Voucher Confirmed',            
'Payment_Voucher_Need_Confirmation'=>'Payment Voucher Need Confirmation',
'Payment_Voucher_Confirmed'=>'Payment Voucher Confirmed',
'OpeningEntries_Need_Confirmation'=>'Opening Entries Need Confirmation',
'OpeningEntries_Confirmed'=>'Opening Entries Confirmed',
'StoresTRansfer_Need_Confirmation'=>'Stores Transfer Need Confirmation',
'StoresTRansfer_Confirmed'=>'Stores Transfer Confirmed',
'RefuseSafeTransfer'=>'Refuse Safe Transfer',
'New_Notifications'=>'New Notifications',
'view_all_notifications'=>'view all notifications',
'PurchasesReturn'=>'Purchases Return',
'SalesReturn'=>'Sales Return',
'Shortmings_Bill'=>'Shortmings Bill',
'Delivery_Request'=>'Delivery Request',
'SalesBillPending'=>'Sales Bill Pending',
'PurchasesBillPending'=>'Purchases Bill Pending',
'NewMeet'=>'New Meet',
'ClientGroup'=>'Client Group',
'HolidayRequest'=>'Holiday Request',
'New_Project'=>'New Project',
'New_Mission'=>'New Mission',
'NewTicketClient'=>'New Ticket Client',
'SolveTicketClient'=>'Solve Ticket Client',
'UnSolveTicketClient'=>'UnSolve Ticket Client',
'NewReservation'=>'New Reservation',
'EndReservation'=>'End Reservation',
'Online_Order'=>'Online Order',
'SalesLowCostPrice'=>'Selling at below cost price',
'ConvertReciptMaintaince'=>'Convert Recipt Maintaince',
'RefuseReciptMaintaince'=>'Refuse Recipt Maintaince',
'EditReciptMaintaince'=>'Edit Recipt Maintaince',
'ConfirmReciptMaintaince'=>'Confirm Recipt Maintaince',
'MaintainceBillReturn'=>'Maintaince Bill Return',
'or'=>'or',
'Intro'=>'Intro',
'Choice_Date'=>'Choice Date',
'Sale'=>'Sale',
'EmpSalaries'=>'Employees Salaries',
'Coin_Credit'=>'Coin Credit',
'Return_Stores_Transfer'=>'Return Stores Transfer',
'Thickness_Print'=>'Thickness Print',
'Height_Print'=>'Height Print',
'job_order_price'=>'job order price',
'EmpPrint'=>'Employee Print',
'JobOrderSechdule'=>'Job Order Sechdule',
'Size'=>'Size',
'Thickness'=>'Thickness',
'JobOrder'=>'Job Order',
'Slaray_Method'=>'Slaray Method',
'EmpAccount'=>'Employee Account',
'Commission_Method'=>'Commission Method',
'CommissionAccount'=>'Commission Account',
'WorthAccount'=>'Worth Account',
'Calories'=>'Calories',
'deposit'=>'Deposit',
'Transfer_To_Sales'=>'Transfer To Sales Bill',
'ExecuteJobOrderSechdule'=>'Execute Job Order Sechdule',
'Workmanship'=>'Workmanship',
'ExecuteJobOrder'=>'Execute Job Order',
'ShowJobOrders'=>'Show Job Orders After Execute',
'EmpCount'=>'Employees Countable',
'ReturnStoresTransfer'=>'Approve Return Stores Transfer',
'Cost_Price'=>' Cost Price Way',
'PriceLowThanCost'=>'The selling price is less than the cost price',
'Average'=>'Average',
'OutcomeManufacturing'=>'Outcome Manufacturing',
'IncomeManufacturing'=>'Income Manufacturing',
'Delete_Purchases'=>'Delete Purchases',
'Edit_Purchases'=>'Edit Purchases',
'Delete_Sales'=>'Delete Sales',
'Edit_Sales'=>'Edit Sales',
'ReturnStoresTransfersSechdule'=>'Return Stores Transfers Sechdule',
'Last_Purch_Price'=>'Last Purchacse Price',
'TrainingCenters'=>'Training Centers',
'CoursesCategory'=>'Courses Category',
'ScientificMaterial'=>'Scientific Material',
'StudentGroup'=>'Student Group',
'CoursesType'=>'Courses Type',
'SpecialCases'=>'Special Cases',
'CoursesHalls'=>'Courses Halls',
'Wiifii'=>'Wiifii',
'Air_Condition'=>'Air Condition',
'HallPlace'=>'Hall Place',
'HallNumber'=>'Hall Number',
'Teachers'=>'Teachers (Instrucator)',        
'Work_Type'=>'Work Type',
'Hour_Price'=>'Hour Price',
'Qualification'=>'Qualification',
'Qualification_Attach'=>'Qualification Attach',
'National_ID'=>'National ID',
'National_ID_Attach'=>'National ID Attach',
'Linked'=>'Linked in',
'Arabic_Bio'=>'Arabic Bio',
'English_Bio'=>'English Bio',       
'Per_Hour'=>'Per Hour',
'Full_Time'=>'Full Time',
'Part_Time'=>'Part Time',
'Single'=>'Single',
'Married'=>'Married',
'Subjects'=>'Subjects',
'Students'=>'Students',
'TeachersSechdule'=>'Teachers Sechdule',
'Dad_Phone'=>'Dad Phone',
'Mom_Phone'=>'Mom Phone',
'Ntional_ID'=>'Ntional ID',
'Case'=>'Is the student with special cases?',
'Special_Case'=>'Special Case',
'Profession'=>'Profession',
'StudentsSechdule'=>'Students Sechdule',
'Courses'=>'Courses',
'Lec_Num'=>'Lecture Number',
'Hours'=>'Hours',
'ReserveCourse'=>'Reserve Course',
'Course'=>'Course',
'Course_Type'=>'Course Type',
'Required_Number'=>'Required Number',
'Total_Required'=>'Total Required',
'Certificate'=>'Certificate',
'Total_Num'=>'Total Number',
'Student'=>'Student',
'StudentReserveCourse'=>'Student Reserve Course',
'ReserveCourseSechdule'=>'Reserve Course Sechdule',
'RegCourses'=>'Regestration Courses Attendence',
'RegCoursesSechdule'=>'Regestration Courses Attendence Sechdule',
'Teacher_Attend'=>'Teacher Attendance',
'Attend'=>' Attendance',
'Bill_View'=>' Bill View',
'ReportIssue'=>'Report Error',
'Traning_Center'=> 'Traning Center',
'Hi'=> 'Hi',
'PurposeTravel'=> 'Purpose Travel',
'Dollar_Value'=> 'Dollar Value',
'CoursesDetails'=> 'Courses Details',
'Courses_Important'=> 'Courses Important',
'Font_Type'=> 'Font Type',
'Font_1'=> 'Font 1',
'Font_2'=> 'Font 2',
'Font_3'=> 'Font 3',
'Font_4'=> 'Font 4',
'Font_5'=> 'Font 5',
'IssueMessage'=> 'Please send problems as below',
'PageName'=> 'Page Name',
'PageLink'=> 'Page Link',
'NewIssue'=> 'New Issue',
'RUSWTONI'=> 'Are You Sure Want to Open New Issue',
'IssueDecs'=> 'Issue Description',
'TypeIssueHere'=> 'Type Issue Here',
'AttachImageIfAvalible'=> 'Attach Image if Avalible',
'ThankUForContactUs'=> 'Thank you for contacting us. We will contact you shortly and the problem will be resolved as soon as possible',
'Attach_Img'=>'Attach Image',            
'Purchases_Order'=>'Purchases Order', 
'PurposeTravel'=>'Purpose Travel',            
'Languages'=>'Languages',            
'Empassies'=>'Empassies',            
'Arabic_Text'=>'Arabic Text',            
'Translate_Text'=>'Translate Text',            
'TranslteModules'=>'Translte Modules',            
'TranslationTourismCompanies'=>'Translation/Tourism Companies',            
'EmpassyReserveDate'=>'Empassy Reserve Date', 
'Purpose'=>'Purpose',            
'Empassy'=>'Empassy',            
'Booking_Date'=>'Booking Date',   
'CommercialRegistration'=>'Commercial Registration',   
'Commercial_Name'=>'Commercial Name',   
'Commercial_Type'=>'Commercial Type',   
'Commercial_Start_Date'=>'Commercial Start Date',   
'Commercial_Number'=>'Commercial Number',   
'Commercial_Capital'=>'Commercial Capital',   
'Commercial_Issuer'=>'Commercial Issuer',   
'Commercial_Address'=>'Commercial Address',   
'Extracted'=>'Extracted',   
'Extracted_Birthplace'=>'Extracted Birthplace',               
'Extracted_Issuer'=>'Extracted Issuer',   
'BirthCertificate'=>'Birth Certificate',   
'MarriageVoucher'=>'Marriage Voucher',   
'FamilyRegistration'=>'Family Registration',   
'Individuals'=>'Individuals',   
'Responsible'=>'Responsible',   
'SimpleRecommendation'=>'Simple Recommendation',   
'ID_Name'=>'ID Name',   
'ID_Profession'=>'ID Profession',   
'ID_Martial_Status'=>'ID Martial_Status',               
'Passport_Name'=>'Passport Name',   
'Passport_Profession'=>'Passport Profession',   
'Passport_Martial_Status'=>'Passport Martial Status',   
'Client_Type'=>'Client Type',   
'From_Lang'=>'From Lang',   
'To_Lang'=>'To Lang',   
'Num_Translted_Word'=>'Number of Translted Word',   
'Client_Type'=>'Client Type',   
'AddTranslate'=>'Add Translate',   
'Commericial'=>'Commericial',   
'Company'=>'Company',                
'Education'=>'Education',                
'Video'=>'Video',                
'Package'=>'Package',                             
'Maximum_Sales_Qty'=>'Maximum Sales Qty',                             
'LimitSalesQty'=>'Limit Sales Qty',                             
'Total_Wight_Bill'=>'Total Wight Bill',                             
'SameNumber'=>'Same Number',                
'Welcome_Arabic_Word_App'=>'Welcome Arabic Word App',                
'Welcome_English_Word_App'=>'Welcome English Word App',                
'ChangePrices'=>'Change Prices',                
'Expired_Subscribe'=>'Expired Subscribe',                
'Duplicate_Items'=>'Duplicate Items in Invoice',                
'MustChoiceBrandOrGroup'=>'Must Choice Brand Or Group',                
'Items_Guide_Store_Show'=>'Items Guide Store Show',                
'AssemblyTotal'=>'Assembly Products Total',                
'Sales_Pro_Desc'=>'Sales Products Describtion ',                
'Print_Type'=>'Print Type ',                
'Bill_Code'=>'Bill Code',                          
'Signture_Name'=>'Signture Name ',                
'Bank_Branch'=>'Bank Branch ',                
            
 
            
      
   
// Static translations to replace dynamic database queries
'Accounting_Manual'=>'Accounting Manual',
'Assets'=>'Assets',
'Start_Period_Sechdule'=>'Start Period Schedule',
'Ledger'=>'Ledger',
'Incom_List'=>'Income List',
'Financial_Center'=>'Financial Center',
'Add_Items'=>'Add Items',
'Items_Guide'=>'Items Guide',
'Stores'=>'Stores',
'Measurement_Units'=>'Measurement Units',
'Items_Groups'=>'Items Groups',
'Manufacture'=>'Manufacture',
'Virables'=>'Variables',
'Sub_Virables'=>'Sub Variables',
'Inventory_Sechdule'=>'Inventory Schedule',
'Settlement_Sechdule'=>'Settlement Schedule',
'Products_Sechdule'=>'Products Schedule',
'Permission_to_exchange_goods'=>'Permission to Exchange Goods',
'Permission_to_receive_goods'=>'Permission to Receive Goods',
'ReceiveGoodsSechdule'=>'Receive Goods Schedule',
'ExchangeGoodsSechdule'=>'Exchange Goods Schedule',
'Consists'=>'Consists',
'ConsistsSechdule'=>'Consists Schedule',
'Activites'=>'Activities',
'Projects'=>'Projects',
'Missions'=>'Missions',
'PurchasesOrder'=>'Purchase Order',
'PurchasesOrderSechdule'=>'Purchase Order Schedule',
'ReciptMaintaince'=>'Maintenance Receipt',
'ReciptMaintainceSechdule'=>'Maintenance Receipt Schedule',
'DeviceDescrips'=>'Device Description',
'Unit'=>'Unit',
'Barcode'=>'Barcode',
'Price_One'=>'Price One',
'Price_Two'=>'Price Two',
'Price_Three'=>'Price Three',
'Minimum'=>'Minimum',
'Maximum'=>'Maximum',
'Code'=>'Code',
'Account_Credit'=>'Account Credit',
'Store'=>'Store',
'Qty'=>'Quantity',

];


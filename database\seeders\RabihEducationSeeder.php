<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RabihEducation;

class RabihEducationSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        // Create sample education videos
        RabihEducation::create([
            'Arabic_Name' => 'مقدمة في نظام OST ERP',
            'English_Name' => 'Introduction to OST ERP System',
            'Video' => 'https://www.youtube.com/embed/sample1',
            'Package' => 1
        ]);

        RabihEducation::create([
            'Arabic_Name' => 'إدارة المخازن',
            'English_Name' => 'Inventory Management',
            'Video' => 'https://www.youtube.com/embed/sample2',
            'Package' => 1
        ]);

        RabihEducation::create([
            'Arabic_Name' => 'النظام المحاسبي',
            'English_Name' => 'Accounting System',
            'Video' => 'https://www.youtube.com/embed/sample3',
            'Package' => 2
        ]);
    }
}

<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

use App\Models\ReciptMaintaince;
use App\Models\PurchasesOrder;
use App\Models\SalesOrder;
use App\Models\Sales;
use App\Models\Quote;
use App\Models\Purchases;
use App\Models\Transltor;
use App\Models\CompanyData;

Route::middleware([
    'web','Lang',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {


Route::group(['middleware' => 'firewall.all'], function () {

    Route::get('/', function () {
        try {
            $Def = CompanyData::orderBy('id','desc')->first();
            if($Def && $Def->View == 0){
                return view('welcome');
            } else {
                return redirect('View');
            }
        } catch (\Exception $e) {
            // If company data doesn't exist or there's a database error, redirect to login
            return redirect('AdminLogin');
        }
    });






            //View

         Route::get('View', 'App\Http\Controllers\WebsiteController@HomePage');

        //ChangeCountrySession
Route::get('ChangeCountrySession/{id}', 'App\Http\Controllers\WebsiteController@ChangeCountrySession');

    //About
Route::get('AboutSite', 'App\Http\Controllers\WebsiteController@AboutSite');

    //ContactSite
Route::get('ContactSite', 'App\Http\Controllers\WebsiteController@ContactSite');

    //PrivacyPolicySite
Route::get('PrivacyPolicySite', 'App\Http\Controllers\WebsiteController@PrivacyPolicySite');

    //TermsSite
Route::get('TermsSite', 'App\Http\Controllers\WebsiteController@TermsSite');

    //FAQSite
Route::get('FAQSite', 'App\Http\Controllers\WebsiteController@FAQSite');

//BlogsSite
    Route::get('BlogsSite', 'App\Http\Controllers\WebsiteController@BlogsSite');
    Route::get('BlogDetails/{id}', 'App\Http\Controllers\WebsiteController@BlogDetails');


//Shop Site
   Route::get('ShopSite', 'App\Http\Controllers\WebsiteController@ShopSite');
   Route::get('ProductDetails/{id}', 'App\Http\Controllers\WebsiteController@ProductDetails');
   Route::get('FilterShopCat/{id}', 'App\Http\Controllers\WebsiteController@FilterShopCat');
   Route::get('ShopFilterBrand/{id}', 'App\Http\Controllers\WebsiteController@ShopFilterBrand');
   Route::get('ShopFilterName', 'App\Http\Controllers\WebsiteController@ShopFilterName');


            //PostMsgRqst
Route::post('PostMsgRqst', 'App\Http\Controllers\WebsiteController@PostMsgRqst');

//Auth Users
    Route::get('LoginSite', 'App\Http\Controllers\WebsiteController@LoginSite');
    Route::post('PostLoginSite', 'App\Http\Controllers\WebsiteController@PostLoginSite');
    Route::get('ForgotSite', 'App\Http\Controllers\WebsiteController@ForgotSite');
    Route::post('PostForgotSite', 'App\Http\Controllers\WebsiteController@PostForgotSite');
    Route::get('RegisterSite', 'App\Http\Controllers\WebsiteController@RegisterSite');
    Route::post('PostRegister', 'App\Http\Controllers\WebsiteController@PostRegister');
    Route::post('PostCodeSite', 'App\Http\Controllers\WebsiteController@PostCodeSite');
    Route::post('PostResetPassword', 'App\Http\Controllers\WebsiteController@PostResetPassword');
    Route::get('LogoutSite', 'App\Http\Controllers\WebsiteController@LogoutSite');

//Qty Filter
   Route::get('ProductDetails/{id}/SiteProQty', 'App\Http\Controllers\WebsiteController@SiteProQty');
   Route::get('ProductDetails/{id}/SiteProQtyV', 'App\Http\Controllers\WebsiteController@SiteProQtyV');
   Route::get('ProductDetails/{id}/SiteProQtyVV', 'App\Http\Controllers\WebsiteController@SiteProQtyVV');


//Cart
    Route::get('CartSite', 'App\Http\Controllers\WebsiteController@CartSite');
    Route::post('AddToCart', 'App\Http\Controllers\WebsiteController@AddToCart');
    Route::post('UpdateCart', 'App\Http\Controllers\WebsiteController@UpdateCart');
    Route::get('DeleteCart/{id}', 'App\Http\Controllers\WebsiteController@DeleteCart');

//Cupon Code
      Route::post('UpdateCuponCode', 'App\Http\Controllers\WebsiteController@UpdateCuponCode');

//Checkout
    Route::get('Checkout', 'App\Http\Controllers\WebsiteController@Checkout');
    Route::get('ChangeAddressSite', 'App\Http\Controllers\WebsiteController@ChangeAddressSite');

//Place Order
   Route::post('PlaceOrder', 'App\Http\Controllers\WebsiteController@PlaceOrder');



//   Gov and City  Filter
        Route::get('GovernrateFilterr/{id}', 'App\Http\Controllers\WebsiteController@GovernrateFilter');
        Route::get('CityFilterr/{id}', 'App\Http\Controllers\WebsiteController@CityFilter');
        Route::get('CityShip/{id}', 'App\Http\Controllers\WebsiteController@CityShip');

Route::group(['middleware' => 'IFAuth:client'], function() {

  //My Account
        Route::get('MyAccountSite', 'App\Http\Controllers\WebsiteController@MyAccountSite');
        Route::post('UpdateAccount', 'App\Http\Controllers\WebsiteController@UpdateAccount');
        Route::post('UpdatePassword', 'App\Http\Controllers\WebsiteController@UpdatePassword');
        Route::post('UpdateAddress', 'App\Http\Controllers\WebsiteController@UpdateAddress');


    //Wishlist
        Route::get('WishlistPage', 'App\Http\Controllers\WebsiteController@WishlistPage');
        Route::get('AddWish/{id}', 'App\Http\Controllers\WebsiteController@AddWish');
        Route::get('DeleteWish/{id}', 'App\Http\Controllers\WebsiteController@DeleteWish');


    //Compare
            Route::get('ComparePage', 'App\Http\Controllers\WebsiteController@ComparePage');
            Route::get('AddCompare/{id}', 'App\Http\Controllers\WebsiteController@AddCompare');
            Route::get('DeleteCompare/{id}', 'App\Http\Controllers\WebsiteController@DeleteCompare');

    // Comment
      Route::post('AddComment', 'App\Http\Controllers\WebsiteController@AddComment');
      Route::post('EditComment', 'App\Http\Controllers\WebsiteController@EditComment');
      Route::get('DeleteComment/{id}', 'App\Http\Controllers\WebsiteController@DeleteComment');

    //Rate
       Route::post('AddRate', 'App\Http\Controllers\WebsiteController@AddRate');
      Route::post('EditRate', 'App\Http\Controllers\WebsiteController@EditRate');




});
   //Guest
       Route::get('GUESTLIST','App\Http\Controllers\AdminController@GUESTLIST');
      Route::get('GUESTLISTFilter','App\Http\Controllers\AdminController@GUESTLISTFilter');


Route::get('SEED', function () {

  Artisan::call('db:seed');


return back();

});


Route::get('CLEAR', function () {

  Artisan::call('cache:clear');
  Artisan::call('view:clear');
  Artisan::call('route:clear');
  Artisan::call('clear:clear-compiled');
  Artisan::call('config:config:cache');

return back();

});


Route::get('Truncate', function () {

    ReciptMaintaince::truncate();
PurchasesOrder::truncate();
SalesOrder::truncate();
Sales::truncate();
Quote::truncate();
Purchases::truncate();


    return back();
});




Route::get('Backup', function(){

return view('Backup');

});
Route::get('DeleteBackup','App\Http\Controllers\AdminController@DeleteBackup');

// === Admin Panel ===

//Language
Route::get('lang/{x}', 'App\Http\Controllers\LangController@Lang');

//Login and Register
Route::get('AdminLogin', 'App\Http\Controllers\AdminController@LoginPage');
Route::post('Login', 'App\Http\Controllers\AdminController@Login');
Route::get('Logout', 'App\Http\Controllers\AdminController@Logout');
Route::get('forgotpassword','App\Http\Controllers\AdminController@forgotpasswordPage');
Route::post('forgotpassword','App\Http\Controllers\AdminController@forgotpassword');
Route::get('reset/password/{token}','App\Http\Controllers\AdminController@reset_password');
Route::post('reset/password/{token}','App\Http\Controllers\AdminController@reset_password_final');

Config::set('auth.defines','admin');
Route::group(['middleware' =>'Admin:admin'], function () {
Route::group(['middleware' =>'auth:admin'], function() {



        Route::group(['middleware' => 'IFGuest:admin'], function() {

    //GusetListPrice
      Route::get('GusetListPrice','App\Http\Controllers\AdminController@GusetListPrice');
      Route::get('PriceListFilter','App\Http\Controllers\AdminController@PriceListFilter');

               });


    Route::group(['middleware' => 'IFNotGuest:admin'], function() {

         Route::get('OstAdmin', function () {
    return view('admin.home');
});


            //Profile
Route::get('Profile','App\Http\Controllers\AdminController@Profile');
Route::post('UpdateAdminProfile/{id?}','App\Http\Controllers\AdminController@UpdateAdminProfile');


        //Backup
        Route::get('serverDBBackup', 'App\Http\Controllers\StoresController@serverDBBackup');

Route::get('BackupDB', function(){

  session()->flash('success',trans('admin.DBBackupSuccessfully'));
Artisan::call('backup:run', ['--only-db' => true]);

return redirect('Backup');

});

    //Filters
     Route::get('AllCustomers', 'App\Http\Controllers\AccountsController@AllCustomers');
     Route::get('AllCustomersJ/{id}', 'App\Http\Controllers\AccountsController@AllCustomersJ');
     Route::get('AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('EditPurchasesOrder/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('TransferToPurchases/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('TransferToPurchasesRecived/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('EditPuechasesBill/AllVendors', 'App\Http\Controllers\AccountsController@AllVendors');
     Route::get('AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('TransferToPurchases/AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('TransferToPurchasesRecived/AllVendorsJ/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJ');
     Route::get('AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('TransferToPurchases/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('TransferToPurchasesRecived/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
     Route::get('AllCoins', 'App\Http\Controllers\AccountsController@AllCoins');
     Route::get('AllCoinsJ/{id}', 'App\Http\Controllers\AccountsController@AllCoinsJ');
     Route::get('AllBanksAccounts', 'App\Http\Controllers\AccountsController@AllBanksAccounts');
     Route::get('AllBanksAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllBanksAccountsJ');
     Route::get('AllCostss', 'App\Http\Controllers\AccountsController@AllCostss');
     Route::get('AllCostssJ/{id}', 'App\Http\Controllers\AccountsController@AllCostssJ');


     Route::get('AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('HoldSale/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('HoldSale/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('AllSubAccountsMsrofat', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofat');
     Route::get('AllSubAccountsMsrofatJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofatJ');
     Route::get('HoldSale/AllSubAccountsMsrofat', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofat');
     Route::get('HoldSale/AllSubAccountsMsrofatJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMsrofatJ');


    Route::get('AllSubAccountsMwrden', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrden');
    Route::get('AllSubAccountsMwrdenJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrdenJ');
    Route::get('HoldSale/AllSubAccountsMwrden', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrden');
    Route::get('HoldSale/AllSubAccountsMwrdenJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsMwrdenJ');

     Route::get('EditJournalizing/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditPayment_Voucher/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditReceipt_Voucher/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditOpening_Entries/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');

     Route::get('EditInvntory/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('EditInvntory/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('SettlementInvntory/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('SettlementInvntory/AllSubAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllSubAccountsJ');
     Route::get('AllAccounts', 'App\Http\Controllers\AccountsController@AllAccounts');
     Route::get('EditJournalizing/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditPayment_Voucher/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditReceipt_Voucher/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');
     Route::get('EditOpening_Entries/AllSubAccounts', 'App\Http\Controllers\AccountsController@AllSubAccounts');

     Route::get('AllMainAccounts', 'App\Http\Controllers\AccountsController@AllMainAccounts');
     Route::get('AllUsers', 'App\Http\Controllers\AccountsController@AllUsers');
     Route::get('AllUsersJ/{id}', 'App\Http\Controllers\AccountsController@AllUsersJ');
     Route::get('AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('TransferSureSafe/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditSafeTransfer/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('TransferSureSafe/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditSafeTransfer/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditPayment_Voucher/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditPayment_Voucher/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');
     Route::get('EditReceipt_Voucher/AllSafes', 'App\Http\Controllers\AccountsController@AllSafes');
     Route::get('EditReceipt_Voucher/AllSafesJ/{id}', 'App\Http\Controllers\AccountsController@AllSafesJ');


     Route::get('SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferToPurchases/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferToPurchasesRecived/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('ReturnSales/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditPayment_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditReceipt_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('TransferSureSafe/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('EditSafeTransfer/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');

     Route::get('SurePayment_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');
     Route::get('SureReceipt_Voucher/SafeBalanceFilter/{id}', 'App\Http\Controllers\AccountsController@SafeBalanceFilter');

     Route::get('AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditPayment_Voucher/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditReceipt_Voucher/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditJournalizing/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');
     Route::get('EditOpening_Entries/AccountNameFilter/{id}', 'App\Http\Controllers\AccountsController@AccountNameFilter');

     Route::get('AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditAttendance/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('HoldSale/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('ESBill/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditQuote/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditSalesOrder/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
     Route::get('EditAttendance/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('HoldSale/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('ESBill/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('EditQuote/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
     Route::get('EditSalesOrder/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');

     Route::get('AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('AllClientsJ/{id}', 'App\Http\Controllers\AccountsController@AllClientsJ');
     Route::get('AllClientsFilterJ/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJ');
     Route::get('EditReciptMaintaince/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('HoldSale/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('ESBill/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesSO/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesExchange/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');

     Route::get('AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditReciptMaintaince/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesSO/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesExchange/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('HoldSale/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditQuote/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('EditSalesOrder/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('ESBill/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');
     Route::get('TransferToSalesExchange/AllClientsFilterJS/{id}', 'App\Http\Controllers\AccountsController@AllClientsFilterJS');


     Route::get('AllShips', 'App\Http\Controllers\AccountsController@AllShips');
     Route::get('AllShipsJ/{id}', 'App\Http\Controllers\AccountsController@AllShipsJ');
     Route::get('AllVend', 'App\Http\Controllers\AccountsController@AllVend');
     Route::get('AllVendJ/{id}', 'App\Http\Controllers\AccountsController@AllVendJ');
     Route::get('AllCli', 'App\Http\Controllers\AccountsController@AllCli');
     Route::get('AllCliJ/{id}', 'App\Http\Controllers\AccountsController@AllCliJ');

     Route::get('EditQuote/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('TransferToSalesExchange/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('EditSalesOrder/AllClientsFilter', 'App\Http\Controllers\AccountsController@AllClientsFilter');
     Route::get('MainAccountss/{id}', 'App\Http\Controllers\AccountsController@MainAccountss');


    Route::get('PurchPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToPurchases/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToPurchasesRecived/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('PurchOrdPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('QuotePrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesOrderPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint8/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('SalesPrint5/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('AddNewVendor/{co}/{name}/{price}', 'App\Http\Controllers\PurchasesController@AddNewVendor');
    Route::get('HoldSale/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
//===== Settings =========================================================================================================

            //User Premations
    Route::get('AdminsPremations', 'App\Http\Controllers\RoleController@AdminsPremationsPage');
    Route::post('AddPrem', 'App\Http\Controllers\RoleController@AddPrem');
    Route::post('EditPrem/{id}', 'App\Http\Controllers\RoleController@EditPrem');
    Route::get('DeletePrem/{id}', 'App\Http\Controllers\RoleController@DeletePrem');


          //Admins
Route::get('Admins', 'App\Http\Controllers\AdminController@AdminsPage');
Route::post('AddAdmin', 'App\Http\Controllers\AdminController@AddAdmin');
Route::post('EditAdmin/{id}', 'App\Http\Controllers\AdminController@EditAdmin');
Route::get('DeleteAdmin/{id}', 'App\Http\Controllers\AdminController@DeleteAdmin');

        //DeleteMoves
        Route::get('DeleteMoves', 'App\Http\Controllers\AdminController@DeleteMoves');
Route::post('PostDeleteMoves', 'App\Http\Controllers\AdminController@PostDeleteMoves');

            //Default_Data
            Route::get('Default_Data', 'App\Http\Controllers\AdminController@Default_DataPage');
            Route::post('AddDefaultCompany', 'App\Http\Controllers\AdminController@AddDefaultCompany');
            Route::get('AddDefaultCompanyFirst', 'App\Http\Controllers\AdminController@AddDefaultCompanyFirst');
            Route::post('AddDefaultAccount', 'App\Http\Controllers\AdminController@AddDefaultAccount');
            Route::get('AddDefaultAccountsFirst', 'App\Http\Controllers\AdminController@AddDefaultAccountsFirst');
            Route::post('AddDefaultStore', 'App\Http\Controllers\AdminController@AddDefaultStore');
            Route::get('AddDefaultStoreFirst', 'App\Http\Controllers\AdminController@AddDefaultStoreFirst');
            Route::post('AddDefaultCrm', 'App\Http\Controllers\AdminController@AddDefaultCrm');
            Route::get('AddDefaultCrmFirst', 'App\Http\Controllers\AdminController@AddDefaultCrmFirst');
            Route::post('AddDefaultPurchases', 'App\Http\Controllers\AdminController@AddDefaultPurchases');
            Route::get('AddDefaultPurchasesFirst', 'App\Http\Controllers\AdminController@AddDefaultPurchasesFirst');
            Route::post('AddDefaultSales', 'App\Http\Controllers\AdminController@AddDefaultSales');
            Route::get('AddDefaultSalesFirst', 'App\Http\Controllers\AdminController@AddDefaultSalesFirst');
            Route::post('AddDefaultShowHide', 'App\Http\Controllers\AdminController@AddDefaultShowHide');
            Route::get('AddDefaultShowHideFirst', 'App\Http\Controllers\AdminController@AddDefaultShowHideFirst');
            Route::post('AddDefaultMaintaince', 'App\Http\Controllers\AdminController@AddDefaultMaintaince');
            Route::get('AddDefaultMaintainceFirst', 'App\Http\Controllers\AdminController@AddDefaultMaintainceFirst');
            Route::post('AddDefaultManufacture', 'App\Http\Controllers\AdminController@AddDefaultManufacture');
            Route::get('AddDefaultManufactureFirst', 'App\Http\Controllers\AdminController@AddDefaultManufactureFirst');
            Route::post('AddDefaultShipping', 'App\Http\Controllers\AdminController@AddDefaultShipping');
            Route::get('AddDefaultShippingFirst', 'App\Http\Controllers\AdminController@AddDefaultShippingFirst');
        //Modules_Settings
          Route::get('Modules_Settings', 'App\Http\Controllers\AdminController@Modules_SettingsPage');
          Route::get('AddDefaultModulesFirst', 'App\Http\Controllers\AdminController@AddDefaultModulesFirst');
          Route::get('AddDefaultModulesNumFirst', 'App\Http\Controllers\AdminController@AddDefaultModulesNumFirst');
          Route::post('AddDefaultModules', 'App\Http\Controllers\AdminController@AddDefaultModules');
          Route::post('AddDefaultModulesNum', 'App\Http\Controllers\AdminController@AddDefaultModulesNum');


        //Translate
          Route::get('Translate', 'App\Http\Controllers\AdminController@TranslatePage');
          Route::post('AddTranslate', 'App\Http\Controllers\AdminController@AddTranslate');
          Route::post('EditTranslate/{id}', 'App\Http\Controllers\AdminController@EditTranslate');
          Route::get('DeleteTranslate/{id}', 'App\Http\Controllers\AdminController@DeleteTranslate');

//==== End Settings =======================================================================================================

//=====  Accounts ==========================================================================================================
        //QR
            Route::get('QR', 'App\Http\Controllers\AdminController@QRPage');

    //Accounting Manual
    Route::get('AccountingManual', 'App\Http\Controllers\AccountsController@AccountingManualPage');
    Route::post('AddAccount', 'App\Http\Controllers\AccountsController@AddAccount');
    Route::post('EditAccount', 'App\Http\Controllers\AccountsController@EditAccount');
    Route::get('DeleteAccount/{id}', 'App\Http\Controllers\AccountsController@DeleteAccount');


                //Cost Centers
Route::get('CostCenters', 'App\Http\Controllers\AccountsController@CostCentersPage');
Route::post('AddCostCenters', 'App\Http\Controllers\AccountsController@AddCostCenters');
Route::post('EditCostCenters/{id}', 'App\Http\Controllers\AccountsController@EditCostCenters');
Route::get('DeleteCostCenters/{id}', 'App\Http\Controllers\AccountsController@DeleteCostCenters');


                    //Coins
Route::get('Coins', 'App\Http\Controllers\AccountsController@CoinsPage');
Route::post('AddCoins', 'App\Http\Controllers\AccountsController@AddCoins');
Route::post('EditCoins/{id}', 'App\Http\Controllers\AccountsController@EditCoins');
Route::get('DeleteCoins/{id}', 'App\Http\Controllers\AccountsController@DeleteCoins');


   //Checks_Type
Route::get('Checks_Type', 'App\Http\Controllers\AccountsController@Checks_TypePage');
Route::post('AddChecks_Type', 'App\Http\Controllers\AccountsController@AddChecks_Type');
Route::post('EditChecks_Type/{id}', 'App\Http\Controllers\AccountsController@EditChecks_Type');
Route::get('DeleteChecks_Type/{id}', 'App\Http\Controllers\AccountsController@DeleteChecks_Type');


//Journalizing
    Route::get('Journalizing', 'App\Http\Controllers\AccountsController@JournalizingPage');
    Route::post('AddJournalizing', 'App\Http\Controllers\AccountsController@AddJournalizing');
    Route::get('JournalizingPrint/{id}', 'App\Http\Controllers\AccountsController@JournalizingPrint');
    Route::get('SureJournalizing/{id}', 'App\Http\Controllers\AccountsController@SureJournalizing');
    Route::post('PostSureJournalizing', 'App\Http\Controllers\AccountsController@PostSureJournalizing');

    //Receipt_Voucher
    Route::get('Receipt_Voucher', 'App\Http\Controllers\AccountsController@Receipt_VoucherPage');
    Route::post('AddReceipt_Voucher', 'App\Http\Controllers\AccountsController@AddReceipt_Voucher');
    Route::get('Receipt_VoucherPrint/{id}', 'App\Http\Controllers\AccountsController@Receipt_VoucherPrint');
Route::get('SureReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@SureReceipt_Voucher');
            Route::post('PostSureReceipt_Voucher', 'App\Http\Controllers\AccountsController@PostSureReceipt_Voucher');

    //Payment_Voucher
    Route::get('Payment_Voucher', 'App\Http\Controllers\AccountsController@Payment_VoucherPage');
    Route::post('AddPayment_Voucher', 'App\Http\Controllers\AccountsController@AddPayment_Voucher');
    Route::get('Payment_VoucherPrint/{id}', 'App\Http\Controllers\AccountsController@Payment_VoucherPrint');
Route::get('SurePayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@SurePayment_Voucher');
            Route::post('PostSurePayment_Voucher', 'App\Http\Controllers\AccountsController@PostSurePayment_Voucher');

//Opening Entries
       Route::get('OpeningEntries', 'App\Http\Controllers\AccountsController@OpeningEntriesPage');
       Route::post('AddOpeningEntries', 'App\Http\Controllers\AccountsController@AddOpeningEntries');
       Route::get('Opening_EntriesPrint/{id}', 'App\Http\Controllers\AccountsController@Opening_EntriesPrint');
Route::get('SureOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@SureOpening_Entries');
            Route::post('PostSureOpeningEntries', 'App\Http\Controllers\AccountsController@PostSureOpeningEntries');

//Exporting_Checks
         Route::get('Exporting_Checks', 'App\Http\Controllers\AccountsController@Exporting_ChecksPage');
         Route::post('AddExporting_Checks', 'App\Http\Controllers\AccountsController@AddExporting_Checks');
         Route::post('EditExporting_Checks/{id}', 'App\Http\Controllers\AccountsController@EditExporting_Checks');
         Route::post('ReasonExportChecks', 'App\Http\Controllers\AccountsController@ReasonExportChecks');
         Route::post('TransExportingChecks', 'App\Http\Controllers\AccountsController@TransExportingChecks');
         Route::get('DeleteExportingChecks/{id}', 'App\Http\Controllers\AccountsController@DeleteExportingChecks');
         Route::get('PrintOutcomChecks/{id}', 'App\Http\Controllers\AccountsController@PrintOutcomChecks');
         Route::post('PayExportingChecks', 'App\Http\Controllers\AccountsController@PayExportingChecks');


//Incoming_checks
         Route::get('Incoming_checks', 'App\Http\Controllers\AccountsController@Incoming_checksPage');
         Route::post('AddIncoming_checks', 'App\Http\Controllers\AccountsController@AddIncoming_checks');
         Route::post('EditIncoming_checks/{id}', 'App\Http\Controllers\AccountsController@EditIncoming_checks');
         Route::post('ReasonIncoming_checks', 'App\Http\Controllers\AccountsController@ReasonIncoming_checks');
         Route::post('TransIncoming_checks', 'App\Http\Controllers\AccountsController@TransIncoming_checks');
         Route::get('DeleteIncoming_checks/{id}', 'App\Http\Controllers\AccountsController@DeleteIncoming_checks');
         Route::get('PrintIncomChecks/{id}', 'App\Http\Controllers\AccountsController@PrintIncomChecks');
         Route::post('PayIncomingChecks', 'App\Http\Controllers\AccountsController@PayIncomingChecks');

    //Insurance_Paper
        Route::get('Insurance_Paper', 'App\Http\Controllers\AccountsController@Insurance_PaperPage');
        Route::post('AddInsurancePaper', 'App\Http\Controllers\AccountsController@AddInsurancePaper');
        Route::get('DeleteInsurancePaper/{id}', 'App\Http\Controllers\AccountsController@DeleteInsurancePaper');
        Route::post('RecivedInurance/{id}', 'App\Http\Controllers\AccountsController@RecivedInurance');


//Safes and Banks
Route::get('Safes_Banks', 'App\Http\Controllers\AccountsController@Safes_BanksPage');
Route::post('AddSafes_Banks', 'App\Http\Controllers\AccountsController@AddSafes_Banks');
Route::post('EditSafes_Banks/{id}', 'App\Http\Controllers\AccountsController@EditSafes_Banks');
Route::get('DeleteSafes_Banks/{id}', 'App\Http\Controllers\AccountsController@DeleteSafes_Banks');



     //Journalizing Sechdule
     Route::get('JournalizingSechdule', 'App\Http\Controllers\AccountsController@JournalizingSechdule');
     Route::get('EditJournalizing/{id}', 'App\Http\Controllers\AccountsController@EditJournalizing');
     Route::get('DeleteJournalizing/{id}', 'App\Http\Controllers\AccountsController@DeleteJournalizing');
     Route::post('PostEditJournalizing', 'App\Http\Controllers\AccountsController@PostEditJournalizing');

      //Receipt Voucher Sechdule
     Route::get('Receipt_VoucherSechdule', 'App\Http\Controllers\AccountsController@Receipt_VoucherSechdule');
     Route::get('EditReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@EditReceipt_Voucher');
     Route::get('DeleteReceipt_Voucher/{id}', 'App\Http\Controllers\AccountsController@DeleteReceipt_Voucher');
     Route::post('PostEditReceipt_Voucher', 'App\Http\Controllers\AccountsController@PostEditReceipt_Voucher');

      //Payment Voucher Sechdule
     Route::get('Payment_VoucherSechdule', 'App\Http\Controllers\AccountsController@Payment_VoucherSechdule');
     Route::get('EditPayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@EditPayment_Voucher');
     Route::get('DeletePayment_Voucher/{id}', 'App\Http\Controllers\AccountsController@DeletePayment_Voucher');
     Route::post('PostEditPayment_Voucher', 'App\Http\Controllers\AccountsController@PostEditPayment_Voucher');

          //Opening Entries Sechdule
     Route::get('Opening_EntriesSechdule', 'App\Http\Controllers\AccountsController@Opening_EntriesSechdule');
     Route::get('EditOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@EditOpening_Entries');
     Route::get('DeleteOpening_Entries/{id}', 'App\Http\Controllers\AccountsController@DeleteOpening_Entries');
     Route::post('PostEditOpeningEntries', 'App\Http\Controllers\AccountsController@PostEditOpening_Entries');
     //SafesTransfer
            Route::get('SafesTransfer', 'App\Http\Controllers\AccountsController@SafesTransferPage');
            Route::get('TransferSureSafe/{id}', 'App\Http\Controllers\AccountsController@TransferSureSafe');
            Route::get('RefusedSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@RefusedSafeTransfer');
            Route::get('EditSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@EditSafeTransfer');
            Route::post('AddSafeTransfer', 'App\Http\Controllers\AccountsController@AddSafeTransfer');
            Route::post('SureSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@SureSafeTransfer');
            Route::post('PostEditSafeTransfer/{id}', 'App\Http\Controllers\AccountsController@PostEditSafeTransfer');
            Route::get('SafesTransferSechdule', 'App\Http\Controllers\AccountsController@SafesTransferSechdulePage');
            Route::get('TransSafePrint/{id}', 'App\Http\Controllers\AccountsController@TransSafePrint');


//Assets
       Route::get('Assets', 'App\Http\Controllers\AccountsController@AssetsPage');
       Route::post('AddAssets', 'App\Http\Controllers\AccountsController@AddAssets');
       Route::post('AssetSale', 'App\Http\Controllers\AccountsController@AssetSale');
       Route::get('DeleteAssets/{id}', 'App\Http\Controllers\AccountsController@DeleteAssets');
       Route::get('AllMainAssetsAccounts', 'App\Http\Controllers\AccountsController@AllMainAssetsAccounts');
       Route::get('AllMainAssetsAccountsJ/{id}', 'App\Http\Controllers\AccountsController@AllMainAssetsAccountsJ');
       Route::get('AllAccountsExpenses', 'App\Http\Controllers\AccountsController@AllAccountsExpenses');
       Route::get('AllAccountsExpensesJ/{id}', 'App\Http\Controllers\AccountsController@AllAccountsExpensesJ');
       Route::get('AllAccountsComplex', 'App\Http\Controllers\AccountsController@AllAccountsComplex');
       Route::get('AllAccountsComplexJ/{id}', 'App\Http\Controllers\AccountsController@AllAccountsComplexJ');

//Assets
       Route::get('AssetExpenses', 'App\Http\Controllers\AccountsController@AssetExpensesPage');
       Route::post('AddAssetsExpenses', 'App\Http\Controllers\AccountsController@AddAssetsExpenses');

//==== End Accounts ==========================================================================================================


//=====  Accounts Reports  =================================================================================================
  //General_Daily
  Route::get('General_Daily', 'App\Http\Controllers\AccountReportsController@General_DailyPage');
  Route::get('GenralDailyFilter', 'App\Http\Controllers\AccountReportsController@GenralDailyFilter');
  Route::get('GenralDailyFilterBond', 'App\Http\Controllers\AccountReportsController@GenralDailyFilterBond');
  Route::get('GeneralDailyFilterTwo', 'App\Http\Controllers\AccountReportsController@GeneralDailyFilterTwo');


  //Trial_Balance
  Route::get('Trial_Balance', 'App\Http\Controllers\AccountReportsController@Trial_BalancePage');
  Route::get('FilterTrial_Balance', 'App\Http\Controllers\AccountReportsController@FilterTrial_Balance');
  Route::get('TrialBalanceFilterTwo', 'App\Http\Controllers\AccountReportsController@TrialBalanceFilterTwo');


  //Account_Balances
  Route::get('Account_Balances', 'App\Http\Controllers\AccountReportsController@Account_BalancesPage');
  Route::get('FilterAccount_Balances', 'App\Http\Controllers\AccountReportsController@FilterAccount_Balances');
  Route::get('AccountBalancesFilterTwo', 'App\Http\Controllers\AccountReportsController@AccountBalancesFilterTwo');

  //Ledger
  Route::get('Ledger', 'App\Http\Controllers\AccountReportsController@LedgerPage');
  Route::get('FilterLedger', 'App\Http\Controllers\AccountReportsController@FilterLedger');
  Route::post('FilterPrintLedger', 'App\Http\Controllers\AccountReportsController@FilterPrintLedger');
  Route::get('LedgerFilterTwo', 'App\Http\Controllers\AccountReportsController@LedgerFilterTwo');

  //Safe_Bank_Statement
  Route::get('Safe_Bank_Statement', 'App\Http\Controllers\AccountReportsController@Safe_Bank_StatementPage');
  Route::get('FilterSafe_Bank_Statement', 'App\Http\Controllers\AccountReportsController@FilterSafe_Bank_Statement');
          Route::get('SafeBankStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@SafeBankStatementFilterTwo');

  //Customer_Balances
  Route::get('Customer_Balances', 'App\Http\Controllers\AccountReportsController@Customer_BalancesPage');
  Route::get('FilterCustomer_Balances', 'App\Http\Controllers\AccountReportsController@FilterCustomer_Balances');
Route::get('CustomerBalancesFilterTwo', 'App\Http\Controllers\AccountReportsController@CustomerBalancesFilterTwo');
  //Vendor_Account_Statement
  Route::get('Vendor_Account_Statement', 'App\Http\Controllers\AccountReportsController@Vendor_Account_StatementPage');
  Route::get('FilterVendor_Account_Statement', 'App\Http\Controllers\AccountReportsController@FilterVendor_Account_Statement');
Route::get('VendorAccountStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@VendorAccountStatementFilterTwo');

  //Customer_Account_Statement
  Route::get('Customer_Account_Statement', 'App\Http\Controllers\AccountReportsController@Customer_Account_StatementPage');
  Route::get('FilterCustomer_Account_Statement', 'App\Http\Controllers\AccountReportsController@FilterCustomer_Account_Statement');
Route::get('CustomerAccountStatementFilterTwo', 'App\Http\Controllers\AccountReportsController@CustomerAccountStatementFilterTwo');
  //Cost_Centers_Report
  Route::get('Cost_Centers_Report', 'App\Http\Controllers\AccountReportsController@Cost_Centers_ReportPage');
  Route::get('Cost_Centers_ReportFilter', 'App\Http\Controllers\AccountReportsController@Cost_Centers_ReportFilter');


  //Checks_Reports
  Route::get('Checks_Reports', 'App\Http\Controllers\AccountReportsController@Checks_ReportsPage');
  Route::get('Checks_ReportsFilter', 'App\Http\Controllers\AccountReportsController@Checks_ReportsFilter');
Route::get('ChecksReportsFilterTwo', 'App\Http\Controllers\AccountReportsController@ChecksReportsFilterTwo');
  //Incom_List
  Route::get('Incom_List', 'App\Http\Controllers\AccountReportsController@Incom_ListPage');
  Route::get('FilterIncom_List', 'App\Http\Controllers\AccountReportsController@FilterIncom_List');

  //Financial_Center
  Route::get('Financial_Center', 'App\Http\Controllers\AccountReportsController@Financial_CenterPage');
  Route::get('FilterFinancial_Center', 'App\Http\Controllers\AccountReportsController@FilterFinancial_Center');

  //Safes_Balances
  Route::get('Safes_Balances', 'App\Http\Controllers\AccountReportsController@Safes_BalancesPage');
  Route::get('FilterSafes_Balances', 'App\Http\Controllers\AccountReportsController@FilterSafes_Balances');


//==== End Accounts Reports=================================================================================================


// === Stores  ========================================================================================================

    //Stores
Route::get('Stores', 'App\Http\Controllers\StoresController@StoresPage');
Route::post('AddStores', 'App\Http\Controllers\StoresController@AddStores');
Route::post('EditStores/{id}', 'App\Http\Controllers\StoresController@EditStores');
Route::get('DeleteStores/{id}', 'App\Http\Controllers\StoresController@DeleteStores');


                        //Measurement_Units
Route::get('Measurement_Units', 'App\Http\Controllers\StoresController@Measurement_UnitsPage');
Route::post('AddMeasurement_Units', 'App\Http\Controllers\StoresController@AddMeasurement_Units');
Route::post('EditMeasurement_Units/{id}', 'App\Http\Controllers\StoresController@EditMeasurement_Units');
Route::get('DeleteMeasurement_Units/{id}', 'App\Http\Controllers\StoresController@DeleteMeasurement_Units');



                    //Manufacture
Route::get('Manufacture', 'App\Http\Controllers\StoresController@ManufacturePage');
Route::post('AddManufacture', 'App\Http\Controllers\StoresController@AddManufacture');
Route::post('EditManufacture/{id}', 'App\Http\Controllers\StoresController@EditManufacture');
Route::get('DeleteManufacture/{id}', 'App\Http\Controllers\StoresController@DeleteManufacture');


                       //Virables
Route::get('Virables', 'App\Http\Controllers\StoresController@VirablesPage');
Route::post('AddVirables', 'App\Http\Controllers\StoresController@AddVirables');
Route::post('EditVirables/{id}', 'App\Http\Controllers\StoresController@EditVirables');
Route::get('DeleteVirables/{id}', 'App\Http\Controllers\StoresController@DeleteVirables');

                           //Sub_Virables
Route::get('Sub_Virables/{id}', 'App\Http\Controllers\StoresController@Sub_VirablesPage');
Route::post('AddSub_Virables', 'App\Http\Controllers\StoresController@AddSub_Virables');
Route::post('EditSub_Virables/{id}', 'App\Http\Controllers\StoresController@EditSub_Virables');
Route::get('DeleteSub_Virables/{id}', 'App\Http\Controllers\StoresController@DeleteSub_Virables');


                       //Taxes
Route::get('Taxes', 'App\Http\Controllers\StoresController@TaxesPage');
Route::post('AddTaxes', 'App\Http\Controllers\StoresController@AddTaxes');
Route::post('EditTaxes/{id}', 'App\Http\Controllers\StoresController@EditTaxes');
Route::get('DeleteTaxes/{id}', 'App\Http\Controllers\StoresController@DeleteTaxes');


                           //SubscribeTypes
Route::get('SubscribeTypes', 'App\Http\Controllers\StoresController@SubscribeTypesPage');
Route::post('AddSubscribeTypes', 'App\Http\Controllers\StoresController@AddSubscribeTypes');
Route::post('EditSubscribeTypes/{id}', 'App\Http\Controllers\StoresController@EditSubscribeTypes');
Route::get('DeleteSubscribeTypes/{id}', 'App\Http\Controllers\StoresController@DeleteSubscribeTypes');


                       //Items_Groups
Route::get('Items_Groups', 'App\Http\Controllers\StoresController@Items_GroupsPage');
Route::post('AddItems_Groups', 'App\Http\Controllers\StoresController@AddItems_Groups');
Route::post('EditItems_Groups/{id}', 'App\Http\Controllers\StoresController@EditItems_Groups');
Route::get('DeleteItems_Groups/{id}', 'App\Http\Controllers\StoresController@DeleteItems_Groups');


    //Products
    Route::get('Add_Items', 'App\Http\Controllers\StoresController@Add_ItemsPage');
    Route::post('PostAddProduct', 'App\Http\Controllers\StoresController@PostAddProduct');
    Route::get('UnitNameFilter/{id}', 'App\Http\Controllers\StoresController@UnitNameFilter');
    Route::get('AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
    Route::get('UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
    Route::get('UnitNameCodeFilterr/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilterr');
    Route::get('VOneFilter', 'App\Http\Controllers\StoresController@VOneFilter');
    Route::get('VTwoFilter', 'App\Http\Controllers\StoresController@VTwoFilter');
    Route::get('AddCheckName', 'App\Http\Controllers\StoresController@AddCheckName');
    Route::get('FilterPSechdule', 'App\Http\Controllers\StoresController@FilterPSechdule');
    Route::get('AddNewProduct/{Name}/{EnName}/{P_Type}/{Brand}/{Group}/{unit}/{Rate}/{Barcode}/{Price}/{Price_Two}/{Price_Three}', 'App\Http\Controllers\StoresController@AddNewProduct');
     Route::get('TaxPriceFilter', 'App\Http\Controllers\StoresController@TaxPriceFilter');

    //Product Schdule
       Route::get('Products_Sechdule', 'App\Http\Controllers\StoresController@Products_SechdulePage');
       Route::get('EditItems/{id}', 'App\Http\Controllers\StoresController@EditItems');
       Route::get('UnActiveItem/{id}', 'App\Http\Controllers\StoresController@UnActiveItem');
       Route::get('ActiveItem/{id}', 'App\Http\Controllers\StoresController@ActiveItem');
       Route::get('DeleteItem/{id}', 'App\Http\Controllers\StoresController@DeleteItem');
       Route::get('DelSubImage/{id}', 'App\Http\Controllers\StoresController@DelSubImage');
       Route::get('EditItems/UnitNameFilter/{id}', 'App\Http\Controllers\StoresController@UnitNameFilter');
       Route::get('EditItems/AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
       Route::get('EditItems/{id}/AssemblyFilter', 'App\Http\Controllers\StoresController@AssemblyFilter');
       Route::get('EditItems/UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
       Route::get('EditItems/UnitNameCodeFilterr/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilterr');
       Route::post('PostEditProduct/{id}', 'App\Http\Controllers\StoresController@PostEditProduct');
       Route::post('MultiDeleteVira', 'App\Http\Controllers\StoresController@MultiDeleteVira');
       Route::get('SerialProductsQtyFilter', 'App\Http\Controllers\StoresController@SerialProductsQtyFilter');
       Route::get('DeleteSerialQty/{id}', 'App\Http\Controllers\StoresController@DeleteSerialQty');
       Route::get('EditSerialQty', 'App\Http\Controllers\StoresController@EditSerialQty');


    //Start Period Products
     Route::get('StartPeriodProducts', 'App\Http\Controllers\StoresController@StartPeriodProductsPage');
     Route::post('AddStartPeriod', 'App\Http\Controllers\StoresController@AddStartPeriod');
     Route::get('StartProductsFilter/{store}', 'App\Http\Controllers\StoresController@StartProductsFilter');
     Route::get('ViraFilter/{id}', 'App\Http\Controllers\StoresController@ViraFilter');
     Route::get('ViraTwoFilter/{id}', 'App\Http\Controllers\StoresController@ViraTwoFilter');
     Route::get('ViraName/{id}', 'App\Http\Controllers\StoresController@ViraName');

       //Start Period Sechdule
       Route::get('StartPeriodSechdule', 'App\Http\Controllers\StoresController@StartPeriodSechdulePage');
       Route::get('EditStartPeriod/{id}', 'App\Http\Controllers\StoresController@EditStartPeriod');
       Route::get('DeleteStartPeriod/{id}', 'App\Http\Controllers\StoresController@DeleteStartPeriod');
       Route::post('PostEditStartPeriod/{id}', 'App\Http\Controllers\StoresController@PostEditStartPeriod');
       Route::get('EditStartPeriod/StartProductsFilter/{store}', 'App\Http\Controllers\StoresController@StartProductsFilter');
       Route::get('EditStartPeriod/ViraFilter/{id}', 'App\Http\Controllers\StoresController@ViraFilter');
       Route::get('EditStartPeriod/ViraTwoFilter/{id}', 'App\Http\Controllers\StoresController@ViraTwoFilter');
       Route::get('EditStartPeriod/ViraName/{id}', 'App\Http\Controllers\StoresController@ViraName');
       Route::get('EditStartPeriod/UnitNameCodeFilter/{id}/{Pro}', 'App\Http\Controllers\StoresController@UnitNameCodeFilter');
       Route::get('StartPeriodPrint/{id}', 'App\Http\Controllers\StoresController@StartPeriodPrint');

       //Inventory
       Route::get('Inventory', 'App\Http\Controllers\StoresController@InventoryPage');
       Route::get('InventoryFilter', 'App\Http\Controllers\StoresController@InventoryFilter');
       Route::Post('AddInventory', 'App\Http\Controllers\StoresController@AddInventory');
       Route::get('UnitNameCodeInventoryFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');
        Route::get('UnitNameCodeInventoryFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
        Route::get('UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');

    //Inventory Sechdule
       Route::get('Inventory_Sechdule', 'App\Http\Controllers\StoresController@Inventory_SechdulePage');
       Route::get('Settlement_Sechdule', 'App\Http\Controllers\StoresController@Settlement_SechdulePage');
       Route::get('DeleteInventory/{id}', 'App\Http\Controllers\StoresController@DeleteInventory');
       Route::get('EditInvntory/{id}', 'App\Http\Controllers\StoresController@EditInvntory');
       Route::get('SettlementInvntory/{id}', 'App\Http\Controllers\StoresController@SettlementInvntory');
       Route::get('EditInvntory/{id}/InventoryFilter', 'App\Http\Controllers\StoresController@InventoryFilter');

    Route::get('EditInvntory/UnitNameCodeInventoryFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');

    Route::get('EditInvntory/UnitNameCodeInventoryFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
    Route::get('EditInvntory/UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');

     Route::Post('PostEditInventory/{id}', 'App\Http\Controllers\StoresController@PostEditInventory');
     Route::Post('PostSettlement/{id}', 'App\Http\Controllers\StoresController@PostSettlement');
     Route::get('InventoryPrint/{id}', 'App\Http\Controllers\StoresController@InventoryPrint');



   //ItemsGuide
      Route::get('ItemsGuide', 'App\Http\Controllers\StoresController@ItemsGuidePage');
      Route::get('ItemsGuide2', 'App\Http\Controllers\StoresController@ItemsGuide2');
      Route::get('UpdatePrice/{id}/{P}/{PP}/{PPP}/{Code}', 'App\Http\Controllers\StoresController@UpdatePrice');
      Route::get('TypeGuideFilter', 'App\Http\Controllers\StoresController@TypeGuideFilter');
      Route::get('GroupGuideFilter', 'App\Http\Controllers\StoresController@GroupGuideFilter');
      Route::get('NameGuideilter', 'App\Http\Controllers\StoresController@NameGuideilter');
      Route::get('CodeGuideFilter', 'App\Http\Controllers\StoresController@CodeGuideFilter');
      Route::post('ChangeProductPrice', 'App\Http\Controllers\StoresController@ChangeProductPrice');



    //StoresTransfers
      Route::get('StoresTransfers', 'App\Http\Controllers\StoresController@StoresTransfersPage');
      Route::get('StoresTransfersSechdule', 'App\Http\Controllers\StoresController@StoresTransfersSechdulePage');

      Route::get('UnitNameCodeStoresTransferFilterTWO/{id}/{pro}/{code}/{store}/{small}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterTWO');
      Route::get('UnitNameCodeStoresTransferFilter/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilter');
      Route::get('UnitNameCodeInventoryFilterSalePrice/{id}/{pro}/{code}/{store}/{PRORATE}', 'App\Http\Controllers\StoresController@UnitNameCodeInventoryFilterSalePrice');
      Route::get('TarnsferStoresFilter', 'App\Http\Controllers\StoresController@TarnsferStoresFilter');
      Route::Post('AddStoresTransfers', 'App\Http\Controllers\StoresController@AddStoresTransfers');
      Route::Post('SureStoresTransfers/{id}', 'App\Http\Controllers\StoresController@SureStoresTransfers');
      Route::Post('PostEditStoresTransfers/{id}', 'App\Http\Controllers\StoresController@PostEditStoresTransfers');
      Route::Post('SureRecivedShipStoresTransfers/{id}', 'App\Http\Controllers\StoresController@SureRecivedShipStoresTransfers');
      Route::get('TransferSure/{id}', 'App\Http\Controllers\StoresController@TransferSure');
      Route::get('RefusedStoreTransfer/{id}', 'App\Http\Controllers\StoresController@RefusedStoreTransfer');
      Route::get('EditStoreTransfer/{id}', 'App\Http\Controllers\StoresController@EditStoreTransfer');
      Route::get('ShippingTransferRecived/{id}', 'App\Http\Controllers\StoresController@ShippingTransferRecived');
      Route::get('StoresTransferPrint/{id}', 'App\Http\Controllers\StoresController@StoresTransferPrint');
      Route::get('DeleteStoresTransfer/{id}', 'App\Http\Controllers\StoresController@DeleteStoresTransfer');
      Route::get('StoreClientAccount', 'App\Http\Controllers\StoresController@StoreClientAccount');


        //Return Stores Transfer
         Route::get('Stores_Sales_Transfers_Sechdule', 'App\Http\Controllers\StoresController@Stores_Sales_Transfers_Sechdule');
         Route::get('ReturnStoresTransfer/{id}', 'App\Http\Controllers\StoresController@ReturnStoresTransfer');
         Route::post('ReturnSaleStoresTransfers', 'App\Http\Controllers\StoresController@ReturnSaleStoresTransfers');
         Route::get('ReturnStoresTransfersSechdule', 'App\Http\Controllers\StoresController@ReturnStoresTransfersSechdule');



    //BarcodeـPrinting and Settings
        Route::get('BarcodeـPrinting', 'App\Http\Controllers\StoresController@BarcodeـPrintingPage');
        Route::post('UpdateBarcodePrint', 'App\Http\Controllers\StoresController@UpdateBarcodePrint');
        Route::get('BarcodeFilter', 'App\Http\Controllers\StoresController@BarcodeFilter');
       //settings
        Route::get('BarcodeـPrinting_Settings', 'App\Http\Controllers\StoresController@BarcodeـPrinting_SettingsPage');
        Route::Post('AddBaPriSett', 'App\Http\Controllers\StoresController@AddBaPriSett');
        Route::Post('EditBaPriSett/{id}', 'App\Http\Controllers\StoresController@EditBaPriSett');
        Route::get('DeleteBaPriSett/{id}', 'App\Http\Controllers\StoresController@DeleteBaPriSett');


    //ShippingCompany
        Route::get('ShippingCompany', 'App\Http\Controllers\StoresController@ShippingCompanyPage');
        Route::post('AddShippingCompany', 'App\Http\Controllers\StoresController@AddShippingCompany');
        Route::post('EditShippingCompany/{id}', 'App\Http\Controllers\StoresController@EditShippingCompany');
        Route::get('DeleteShippingCompany/{id}', 'App\Http\Controllers\StoresController@DeleteShippingCompany');

        //Permission_to_exchange_goods
        Route::get('Permission_to_exchange_goods', 'App\Http\Controllers\StoresController@Permission_to_exchange_goods');

        Route::get('ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');
        Route::get('EditPremissionExchange/ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');
        Route::get('EditPremissionRercived/ExchangeProductsFilter', 'App\Http\Controllers\StoresController@ExchangeProductsFilter');

        Route::post('AddExchangeGoods', 'App\Http\Controllers\StoresController@AddExchangeGoods');
        Route::get('ExchangePrint/{id}', 'App\Http\Controllers\StoresController@ExchangePrintPage');
        Route::get('DeletePremissionExchange/{id}', 'App\Http\Controllers\StoresController@DeletePremissionExchange');
        Route::get('ExchangeGoodsSechdule', 'App\Http\Controllers\StoresController@ExchangeGoodsSechdulePage');
        Route::get('EditPremissionExchange/{id}', 'App\Http\Controllers\StoresController@EditPremissionExchange');
        Route::post('PostEditExchangeGoods/{id}', 'App\Http\Controllers\StoresController@PostEditExchangeGoods');
        Route::get('TransferToSalesExchange/{id}', 'App\Http\Controllers\StoresController@TransferToSalesExchange');

    //Permission_to_receive_goods
       Route::get('Permission_to_receive_goods', 'App\Http\Controllers\StoresController@Permission_to_receive_goods');
       Route::post('AddRecivedGoods', 'App\Http\Controllers\StoresController@AddRecivedGoods');
       Route::get('RecivedPrint/{id}', 'App\Http\Controllers\StoresController@RecivedPrint');
       Route::get('ReceiveGoodsSechdule', 'App\Http\Controllers\StoresController@ReceiveGoodsSechdule');
        Route::get('DeletePremissionRercived/{id}', 'App\Http\Controllers\StoresController@DeletePremissionRercived');
        Route::get('EditPremissionRercived/{id}', 'App\Http\Controllers\StoresController@EditPremissionRercived');
        Route::get('TransferToPurchasesRecived/{id}', 'App\Http\Controllers\StoresController@TransferToPurchasesRecived');
        Route::post('PostEditRecivedGoods/{id}', 'App\Http\Controllers\StoresController@PostEditRecivedGoods');

    //Consists
       Route::get('Consists', 'App\Http\Controllers\StoresController@ConsistsPage');
       Route::get('ConsistFilter', 'App\Http\Controllers\StoresController@ConsistFilter');
       Route::post('AddConsists', 'App\Http\Controllers\StoresController@AddConsists');
       Route::get('ConsistsSechdule', 'App\Http\Controllers\StoresController@ConsistsSechdule');


        //StoresQty
          Route::get('StoresQty', 'App\Http\Controllers\StoresController@StoresQtyPage');
          Route::get('StoresQtyFilter', 'App\Http\Controllers\StoresController@StoresQtyFilter');
          Route::get('ChangeStroeQty', 'App\Http\Controllers\StoresController@ChangeStroeQty');
          Route::get('DeleteStoreQty/{id}', 'App\Http\Controllers\StoresController@DeleteStoreQty');


// ===  End Stores  ========================================================================================================

// === HR ========================================================================================================


        //Work Departments
Route::get('WorkDepartments', 'App\Http\Controllers\HRController@WorkDepartmentsPage');
Route::post('AddWorkDepartments', 'App\Http\Controllers\HRController@AddWorkDepartments');
Route::post('EditWorkDepartments', 'App\Http\Controllers\HRController@EditWorkDepartments');
Route::get('DeleteWorkDepartments', 'App\Http\Controllers\HRController@DeleteWorkDepartments');
Route::get('EditDepartment', 'App\Http\Controllers\HRController@EditDepartment');

        //Employment_levels
        Route::get('Employment_levels', 'App\Http\Controllers\HRController@Employment_levels');
Route::post('AddEmployment_levels', 'App\Http\Controllers\HRController@AddEmployment_levels');
Route::post('EditEmployment_levels', 'App\Http\Controllers\HRController@EditEmployment_levels');
Route::get('DeleteEmployment_levels', 'App\Http\Controllers\HRController@DeleteEmployment_levels');


        //Insurance_companies
        Route::get('Insurance_companies', 'App\Http\Controllers\HRController@Insurance_companies');
Route::post('AddInsurance_companies', 'App\Http\Controllers\HRController@AddInsurance_companies');
Route::post('EditInsurance_companies', 'App\Http\Controllers\HRController@EditInsurance_companies');
Route::get('DeleteInsurance_companies', 'App\Http\Controllers\HRController@DeleteInsurance_companies');


            //Jobs_Type
Route::get('Jobs_Type', 'App\Http\Controllers\HRController@Jobs_TypePage');
Route::post('AddJobs_Type', 'App\Http\Controllers\HRController@AddJobs_Type');
Route::post('EditJobs_Type/{id}', 'App\Http\Controllers\HRController@EditJobs_Type');
Route::get('DeleteJobs_Type/{id}', 'App\Http\Controllers\HRController@DeleteJobs_Type');


            //Benefits_Types
Route::get('Benefits_Types', 'App\Http\Controllers\HRController@Benefits_TypesPage');
Route::post('AddBenefits_Types', 'App\Http\Controllers\HRController@AddBenefits_Types');
Route::post('EditBenefits_Types/{id}', 'App\Http\Controllers\HRController@EditBenefits_Types');
Route::get('DeleteBenefits_Types/{id}', 'App\Http\Controllers\HRController@DeleteBenefits_Types');

            //Deductions_Types
Route::get('Deductions_Types', 'App\Http\Controllers\HRController@Deductions_TypesPage');
Route::post('AddDeductions_Types', 'App\Http\Controllers\HRController@AddDeductions_Types');
Route::post('EditDeductions_Types/{id}', 'App\Http\Controllers\HRController@EditDeductions_Types');
Route::get('DeleteDeductions_Types/{id}', 'App\Http\Controllers\HRController@DeleteDeductions_Types');

            //Holidays_Types
Route::get('Holidays_Types', 'App\Http\Controllers\HRController@Holidays_TypesPage');
Route::post('AddHolidays_Types', 'App\Http\Controllers\HRController@AddHolidays_Types');
Route::post('EditHolidays_Types/{id}', 'App\Http\Controllers\HRController@EditHolidays_Types');
Route::get('DeleteHolidays_Types/{id}', 'App\Http\Controllers\HRController@DeleteHolidays_Types');

            //Overtime
Route::get('Overtime', 'App\Http\Controllers\HRController@OvertimePage');
Route::post('AddOvertime', 'App\Http\Controllers\HRController@AddOvertime');
Route::post('EditOvertime/{id}', 'App\Http\Controllers\HRController@EditOvertime');
Route::get('DeleteOvertime/{id}', 'App\Http\Controllers\HRController@DeleteOvertime');

//Employee
    Route::get('AddEmp', 'App\Http\Controllers\HRController@AddEmpPage');
    Route::get('EmpSechdule', 'App\Http\Controllers\HRController@EmpSechdulePage');
    Route::get('JobRequestsSechdule', 'App\Http\Controllers\HRController@JobRequestsSechdule');
    Route::get('EditEmp/{id}', 'App\Http\Controllers\HRController@EditEmp');
    Route::get('DeleteEmp/{id}', 'App\Http\Controllers\HRController@DeleteEmp');
    Route::post('PostAddEmp', 'App\Http\Controllers\HRController@PostAddEmp');
    Route::post('PostEditEmp/{id}', 'App\Http\Controllers\HRController@PostEditEmp');
    Route::get('TransToEmp/{id}', 'App\Http\Controllers\HRController@TransToEmp');
    Route::get('PrintEmp/{id}', 'App\Http\Controllers\HRController@PrintEmp');
    Route::get('PrintCardEmp/{id}', 'App\Http\Controllers\HRController@PrintCardEmp');

            //Loan_Types
Route::get('Loan_Types', 'App\Http\Controllers\HRController@Loan_TypesPage');
Route::post('AddLoan_Types', 'App\Http\Controllers\HRController@AddLoan_Types');
Route::post('EditLoan_Types/{id}', 'App\Http\Controllers\HRController@EditLoan_Types');
Route::get('DeleteLoan_Types/{id}', 'App\Http\Controllers\HRController@DeleteLoan_Types');


    //Borrows
         Route::get('Borrow', 'App\Http\Controllers\HRController@BorrowPage');
         Route::get('AddBorrow', 'App\Http\Controllers\HRController@AddBorrowPage');
         Route::get('EmpCheck/{Emp}/{Month}', 'App\Http\Controllers\HRController@EmpCheck');
         Route::post('PostAddBorrow', 'App\Http\Controllers\HRController@PostAddBorrow');
         Route::get('DeleteBorrow/{id}', 'App\Http\Controllers\HRController@DeleteBorrow');

                //Entitlements
Route::get('Entitlements', 'App\Http\Controllers\HRController@EntitlementsPage');
Route::post('AddEntitlements', 'App\Http\Controllers\HRController@AddEntitlements');
Route::post('EditEntitlements/{id}', 'App\Http\Controllers\HRController@EditEntitlements');
Route::get('DeleteEntitlements/{id}', 'App\Http\Controllers\HRController@DeleteEntitlements');

                //Deducation
Route::get('Deducation', 'App\Http\Controllers\HRController@DeducationPage');
Route::post('AddDeducation', 'App\Http\Controllers\HRController@AddDeducation');
Route::post('EditDeducation/{id}', 'App\Http\Controllers\HRController@EditDeducation');
Route::get('DeleteDeducation/{id}', 'App\Http\Controllers\HRController@DeleteDeducation');


    //Holidays
    Route::get('Holidays', 'App\Http\Controllers\HRController@HolidaysPage');
    Route::post('AddHolidays', 'App\Http\Controllers\HRController@AddHolidays');
    Route::post('EditHolidays/{id}', 'App\Http\Controllers\HRController@EditHolidays');
    Route::get('DeleteHolidays/{id}', 'App\Http\Controllers\HRController@DeleteHolidays');
    Route::get('HolidaysTypeFilter/{type}', 'App\Http\Controllers\HRController@HolidaysTypeFilter');


        //HolidaysOrder
    Route::get('HolidaysOrder', 'App\Http\Controllers\HRController@HolidaysOrderPage');
    Route::post('AddHolidaysOrder', 'App\Http\Controllers\HRController@AddHolidaysOrder');
    Route::post('EditHolidaysOrder/{id}', 'App\Http\Controllers\HRController@EditHolidaysOrder');
    Route::get('DeleteHolidaysOrder/{id}', 'App\Http\Controllers\HRController@DeleteHolidaysOrder');
    Route::get('TransToHoilday/{id}', 'App\Http\Controllers\HRController@TransToHoilday');
    Route::get('HolidaysOrderTypeFilter/{type}', 'App\Http\Controllers\HRController@HolidaysOrderTypeFilter');


//Attendance
    Route::get('Attendance', 'App\Http\Controllers\HRController@AttendancePage');
    Route::get('AttendanceSechdule', 'App\Http\Controllers\HRController@AttendanceSechdulePage');
    Route::get('EmpNameFilter/{Emp}', 'App\Http\Controllers\HRController@EmpNameFilter');
    Route::get('EditAttendance/EmpNameFilter/{Emp}', 'App\Http\Controllers\HRController@EmpNameFilter');
    Route::post('AddAttendance', 'App\Http\Controllers\HRController@AddAttendance');
    Route::post('PostEditAttendance/{id}', 'App\Http\Controllers\HRController@PostEditAttendance');
    Route::get('EditAttendance/{id}', 'App\Http\Controllers\HRController@EditAttendancePage');
    Route::get('DeleteAttendance/{id}', 'App\Http\Controllers\HRController@DeleteAttendance');

    //Departure
      Route::get('Departure/{id}', 'App\Http\Controllers\HRController@DeparturePage');
      Route::post('AddDeparture/{id}', 'App\Http\Controllers\HRController@AddDeparture');
      Route::get('DepartureSechdule', 'App\Http\Controllers\HRController@DepartureSechdulePage');
      Route::get('EditDeparture/{id}', 'App\Http\Controllers\HRController@EditDeparturePage');
      Route::post('PostEditDeparture/{id}', 'App\Http\Controllers\HRController@PostEditDeparture');


    //RegOverTime
     Route::get('RegOverTime', 'App\Http\Controllers\HRController@RegOverTimePage');
     Route::post('AddRegOverTime', 'App\Http\Controllers\HRController@AddRegOverTime');
     Route::post('EditRegOverTime/{id}', 'App\Http\Controllers\HRController@EditRegOverTime');
     Route::get('DeleteRegOverTime/{id}', 'App\Http\Controllers\HRController@DeleteRegOverTime');
     Route::get('OverTimeTypeFilter/{type}/{emp}', 'App\Http\Controllers\HRController@OverTimeTypeFilter');


  //Loan
     Route::get('AddLoan', 'App\Http\Controllers\HRController@AddLoanPage');
     Route::post('PostAddLoan', 'App\Http\Controllers\HRController@PostAddLoan');
     Route::get('Loan', 'App\Http\Controllers\HRController@LoanPage');
     Route::get('DeleteLoan/{id}', 'App\Http\Controllers\HRController@DeleteLoan');

    //EmpInstallment
     Route::get('EmpInstallment', 'App\Http\Controllers\HRController@EmpInstallmentPage');
       Route::get('EmpInstallBillDone/{id}', 'App\Http\Controllers\HRController@EmpInstallBillDone');
        Route::get('EmpUnInstallBill/{id}', 'App\Http\Controllers\HRController@EmpUnInstallBill');
        Route::post('EmpInstallDone', 'App\Http\Controllers\HRController@EmpInstallDone');
        Route::get('EmpUnInstall/{id}', 'App\Http\Controllers\HRController@EmpUnInstall');
        Route::get('InstallEmpPrint/{id}/{inst}', 'App\Http\Controllers\HRController@InstallEmpPrint');


//Salary
     Route::get('AddSalary', 'App\Http\Controllers\HRController@AddSalaryPage');
     Route::get('SalarySechdules', 'App\Http\Controllers\HRController@SalarySechdulesPage');
     Route::get('EmpCheckSalary/{Emp}/{Month}', 'App\Http\Controllers\HRController@EmpCheckSalary');
     Route::post('PostAddSalary', 'App\Http\Controllers\HRController@PostAddSalary');
     Route::get('DeletePaySalary/{id}', 'App\Http\Controllers\HRController@DeletePaySalary');


//ExchangeCommissions
     Route::get('ExchangeCommissions', 'App\Http\Controllers\HRController@ExchangeCommissionsPage');
    Route::get('EmpCheckCommision/{Emp}', 'App\Http\Controllers\HRController@EmpCheckCommision');
    Route::post('PostExchangeCommissions', 'App\Http\Controllers\HRController@PostExchangeCommissions');
    Route::get('ExchangeCommissionsSechdule', 'App\Http\Controllers\HRController@ExchangeCommissionsSechdule');
    Route::get('DeleteCommission/{id}', 'App\Http\Controllers\HRController@DeleteCommission');


        //MyGoals
         Route::get('MyGoals', 'App\Http\Controllers\HRController@MyGoals');

        //ResignationRequest
    Route::get('ResignationRequestSechdule', 'App\Http\Controllers\HRController@ResignationRequestSechdule');
    Route::get('ResignationRequest', 'App\Http\Controllers\HRController@ResignationRequest');
    Route::post('AddResignationRequest', 'App\Http\Controllers\HRController@AddResignationRequest');
    Route::post('EditResignationRequest/{id}', 'App\Http\Controllers\HRController@EditResignationRequest');
    Route::get('DeleteResignationRequest/{id}', 'App\Http\Controllers\HRController@DeleteResignationRequest');
    Route::get('AcceptResignation/{id}', 'App\Http\Controllers\HRController@AcceptResignation');
    Route::get('RefuseResignation', 'App\Http\Controllers\HRController@RefuseResignation');

        //Disclaimer
        Route::get('Disclaimer', 'App\Http\Controllers\HRController@Disclaimer');
           Route::post('AddDisclaimer', 'App\Http\Controllers\HRController@AddDisclaimer');
    Route::post('EditDisclaimer/{id}', 'App\Http\Controllers\HRController@EditDisclaimer');
    Route::get('DeleteDisclaimer/{id}', 'App\Http\Controllers\HRController@DeleteDisclaimer');
    Route::get('PrintDisclaimer/{id}', 'App\Http\Controllers\HRController@PrintDisclaimer');

// === End HR ===================================================================================================


// === Purchases ===================================================================================================

    // == Vendors ==
    Route::get('Vendors', 'App\Http\Controllers\PurchasesController@VendorsPage');
    Route::post('AddVendors', 'App\Http\Controllers\PurchasesController@AddVendors');
    Route::post('EditVendors/{id}', 'App\Http\Controllers\PurchasesController@EditVendors');
    Route::get('DeleteVendors/{id}', 'App\Http\Controllers\PurchasesController@DeleteVendors');

    //  ====  Purchases Order ====
        Route::get('PurchasesOrder', 'App\Http\Controllers\PurchasesController@PurchasesOrderPage');
        Route::get('PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
        Route::get('PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');
        Route::get('UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');
        Route::get('AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
        Route::get('StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');
        Route::get('TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');
        Route::get('ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');
        Route::post('AddPurchasesOrder', 'App\Http\Controllers\PurchasesController@AddPurchasesOrder');
        Route::get('PurchasesOrderSechdule', 'App\Http\Controllers\PurchasesController@PurchasesOrderSechdule');
        Route::get('EditPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@EditPurchasesOrder');
        Route::get('DeletePurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@DeletePurchasesOrder');
        Route::get('PurchOrdPrint/{id}', 'App\Http\Controllers\PurchasesController@PurchOrdPrint');
       Route::get('FilterBillPurchasesOrder', 'App\Http\Controllers\PurchasesController@FilterBillPurchasesOrder');

        Route::get('EditPurchasesOrder/{id}/PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
        Route::get('EditPurchasesOrder/{id}/PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');

        Route::get('EditPurchasesOrder/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

        Route::get('EditManufacturingModel/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

        Route::get('EditPurchasesOrder/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');


        Route::get('EditPurchasesOrder/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

        Route::get('EditPurchasesOrder/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('HoldSale/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');
        Route::get('OpenMaintainceBill/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');

        Route::get('EditPurchasesOrder/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
        Route::get('TransferToPurchases/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
        Route::get('TransferToPurchasesRecived/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');

        Route::get('EditManufacturingModel/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

        Route::get('EditPurchasesOrder/ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');

        Route::get('EditPurchasesOrder/ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');

     Route::post('PostEditPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@PostEditPurchasesOrder');
     Route::post('PostTransferPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchases');
     Route::post('PostTransferPurchasesRecived/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchasesRecived');

      Route::get('TransferToPurchases/{id}', 'App\Http\Controllers\PurchasesController@TransferToPurchases');
      Route::post('PostQuality/{id}', 'App\Http\Controllers\PurchasesController@PostQuality');
      Route::get('PurchOrderQty/{id}', 'App\Http\Controllers\PurchasesController@PurchOrderQtyPage');
      Route::post('RenewalPurchasesOrder/{id}', 'App\Http\Controllers\PurchasesController@RenewalPurchasesOrder');


    //==== End Purchases Order =======

    //Change Price Unit Ajax
     Route::get('ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');
     Route::get('EditPurchasesOrder/ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');

    //Purchases
       Route::get('Purchases', 'App\Http\Controllers\PurchasesController@PurchasesPage');
       Route::get('PurchasesSechdule', 'App\Http\Controllers\PurchasesController@PurchasesSechdulePage');
       Route::get('PurchasesSechduleTax', 'App\Http\Controllers\PurchasesController@PurchasesSechduleTax');
       Route::post('AddPurchases', 'App\Http\Controllers\PurchasesController@AddPurchases');
       Route::get('PurchPrint/{id}', 'App\Http\Controllers\PurchasesController@PurchPrint');
       Route::get('ReturnPurch/{id}', 'App\Http\Controllers\PurchasesController@ReturnPurchPage');
       Route::get('ReturnPurchasesSechdule', 'App\Http\Controllers\PurchasesController@ReturnPurchasesSechdulePage');
       Route::get('FilterBillPurchases', 'App\Http\Controllers\PurchasesController@FilterBillPurchases');
       Route::get('FilterBillPurchasesHold', 'App\Http\Controllers\PurchasesController@FilterBillPurchasesHold');
       Route::post('PostReturnPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostReturnPurchases');
       Route::get('Barcode/{id}', 'App\Http\Controllers\PurchasesController@BarcodePage');
       Route::get('EditPuechasesBill/{id}', 'App\Http\Controllers\PurchasesController@EditPuechasesBillPage');
       Route::get('DeletePurchaseBill/{id}', 'App\Http\Controllers\PurchasesController@DeletePurchaseBill');


       Route::get('StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');
       Route::get('EditPuechasesBill/StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');
       Route::get('EditPurchasesOrder/StorePricePurchasesFilter', 'App\Http\Controllers\PurchasesController@StorePricePurchasesFilter');


       Route::get('EditPuechasesBill/{id}/PurchacesProductsFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsFilter');
       Route::get('EditPuechasesBill/{id}/PurchacesProductsSearchCodeFilter', 'App\Http\Controllers\PurchasesController@PurchacesProductsSearchCodeFilter');

       Route::get('EditPuechasesBill/UnitPurchasesFilter/{id}/{Pro}/{store}/{code}', 'App\Http\Controllers\PurchasesController@UnitPurchasesFilter');

       Route::get('EditPuechasesBill/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');

       Route::get('EditPuechasesBill/StoreNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@StoreNamePurchasesFilter');

       Route::get('EditPuechasesBill/TaxNamePurchasesFilter/{id}', 'App\Http\Controllers\PurchasesController@TaxNamePurchasesFilter');

       Route::get('EditPuechasesBill/AllVendorsJS/{id}', 'App\Http\Controllers\AccountsController@AllVendorsJS');
       Route::get('EditPuechasesBill/ViraFilterPurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraFilterPurchases');

       Route::get('EditPuechasesBill/ViraNamePurchases/{id}', 'App\Http\Controllers\PurchasesController@ViraNamePurchases');

       Route::get('EditPuechasesBill/AddNewVendor/{co}/{name}/{price}', 'App\Http\Controllers\PurchasesController@AddNewVendor');

       Route::get('EditPuechasesBill/AddNewProduct/{Name}/{EnName}/{P_Type}/{Brand}/{Group}/{unit}/{Rate}/{Barcode}/{Price}/{Price_Two}/{Price_Three}', 'App\Http\Controllers\StoresController@AddNewProduct');


       Route::get('EditPuechasesBill/EditPurchasesOrder/ChangePriceUnit/{Pone}/{Ptwo}/{Pthree}/{id}', 'App\Http\Controllers\PurchasesController@ChangePriceUnit');

       Route::post('PostEditPurchasesBill', 'App\Http\Controllers\PurchasesController@PostEditPurchasesBill');

       //PurchasesHold
       Route::get('PurchasesHold', 'App\Http\Controllers\PurchasesController@PurchasesHoldPage');
       Route::get('RecivedPurch/{id}', 'App\Http\Controllers\PurchasesController@RecivedPurchPage');
       Route::post('PostRecivedPurchases/{id}', 'App\Http\Controllers\PurchasesController@PostRecivedPurchases');

       //Recived_Products
       Route::get('Recived_Products', 'App\Http\Controllers\PurchasesController@Recived_ProductsPage');
       Route::get('DeleteRecivedProducts/{id}', 'App\Http\Controllers\PurchasesController@DeleteRecivedProducts');

    //Shortcomings
        Route::get('Shortcomings', 'App\Http\Controllers\PurchasesController@ShortcomingsPage');
        Route::get('ShortcomingsFilter', 'App\Http\Controllers\PurchasesController@ShortcomingsProductsFilter');
        Route::post('AddShortcomings', 'App\Http\Controllers\PurchasesController@AddShortcomings');
        Route::get('ShortcomingsPrint/{id}', 'App\Http\Controllers\PurchasesController@ShortcomingsPrintPage');
        Route::get('ShortcomingsSechdule', 'App\Http\Controllers\PurchasesController@ShortcomingsSechdulePage');
        Route::get('EditShortcomings', 'App\Http\Controllers\PurchasesController@EditShortcomingsPage');
        Route::get('DeleteShortcomings/{id}', 'App\Http\Controllers\PurchasesController@DeleteShortcomings');
        Route::get('TransferToPurchasesShortcomings/{id}', 'App\Http\Controllers\PurchasesController@TransferToPurchasesShortcomings');
        Route::post('PostEditShortcomings', 'App\Http\Controllers\PurchasesController@PostEditShortcomings');
        Route::post('PostTransferPurchasesShortcomings/{id}', 'App\Http\Controllers\PurchasesController@PostTransferPurchasesShortcomings');

        Route::get('ManuProdsFilter', 'App\Http\Controllers\PurchasesController@ManuProdsFilter');

// === End Purchases ===================================================================================================


//==== Sales ==============================================================================================================


                    //Clients_Group
Route::get('Clients_Group', 'App\Http\Controllers\SalesController@Clients_GroupPage');
Route::post('AddClients_Group', 'App\Http\Controllers\SalesController@AddClients_Group');
Route::post('EditClients_Group/{id}', 'App\Http\Controllers\SalesController@EditClients_Group');
Route::get('DeleteClients_Group/{id}', 'App\Http\Controllers\SalesController@DeleteClients_Group');


                            //InstallmentCompanies
Route::get('InstallmentCompanies', 'App\Http\Controllers\SalesController@InstallmentCompanies');
Route::post('AddInstallmentCompanies', 'App\Http\Controllers\SalesController@AddInstallmentCompanies');
Route::post('EditInstallmentCompanies/{id}', 'App\Http\Controllers\SalesController@EditInstallmentCompanies');
Route::get('DeleteInstallmentCompanies/{id}', 'App\Http\Controllers\SalesController@DeleteInstallmentCompanies');


          //Clients
                    Route::get('Clients', 'App\Http\Controllers\SalesController@ClientsPage');
                    Route::get('AddClients', 'App\Http\Controllers\SalesController@AddClientsPage');
                    Route::post('PostAddClients', 'App\Http\Controllers\SalesController@PostAddClients');
                    Route::get('EditClients/{id}', 'App\Http\Controllers\SalesController@EditClientsPage');
                    Route::post('PostEditClients/{id}', 'App\Http\Controllers\SalesController@PostEditClients');
                    Route::get('DeleteClients/{id}', 'App\Http\Controllers\SalesController@DeleteClients');
                    Route::get('GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');
                    Route::get('PlatformFilter/{id}', 'App\Http\Controllers\SalesController@PlatformFilter');
                    Route::get('EditClients/GovernrateFilter/{id}', 'App\Http\Controllers\SalesController@GovernrateFilter');
                    Route::get('EditClients/CityFilter/{id}', 'App\Http\Controllers\SalesController@CityFilter');
                    Route::get('EditClients/PlatformFilter/{id}', 'App\Http\Controllers\SalesController@PlatformFilter');
                    Route::get('DeleteClientFile/{id}', 'App\Http\Controllers\SalesController@DeleteClientFile');
                    Route::get('FilterClientSechdule', 'App\Http\Controllers\SalesController@FilterClientSechdule');
    //Tickets
     Route::get('Tickets/{id}', 'App\Http\Controllers\SalesController@TicketsPage');
     Route::post('AddTicket', 'App\Http\Controllers\SalesController@AddTicket');
     Route::post('EditTicket/{id}', 'App\Http\Controllers\SalesController@EditTicket');
     Route::get('DeleteTicket/{id}', 'App\Http\Controllers\SalesController@DeleteTicket');
     Route::get('SolveTicket/{id}', 'App\Http\Controllers\SalesController@SolveTicket');
     Route::get('UnSolveTicket/{id}', 'App\Http\Controllers\SalesController@UnSolveTicket');



    //Quote
       Route::get('Quote', 'App\Http\Controllers\SalesController@QuotePage');
       Route::get('SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
       Route::get('UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
       Route::get('HoldSale/UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');

       Route::get('AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('TransferToSalesExchange/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
       Route::post('AddQuote', 'App\Http\Controllers\SalesController@AddQuote');
       Route::get('QuotePrint/{id}', 'App\Http\Controllers\SalesController@QuotePrintPage');
       Route::get('Quote_Sechdule', 'App\Http\Controllers\SalesController@Quote_Sechdule');
       Route::get('DeleteQuote/{id}', 'App\Http\Controllers\SalesController@DeleteQuote');
       Route::get('EditQuote/{id}', 'App\Http\Controllers\SalesController@EditQuote');
       Route::get('TransferToSalesOrder/{id}', 'App\Http\Controllers\SalesController@TransferToSalesOrder');

       Route::get('TransferToSales/{id}', 'App\Http\Controllers\SalesController@TransferToSales');

       Route::get('EditQuote/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('EditQuote/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
       Route::get('EditSalesOrder/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
       Route::get('EditSalesOrder/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');

       Route::get('EditQuote/UnitSalesFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
       Route::get('EditQuote/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('HoldSale/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('ReturnSales/AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
       Route::get('EditQuote/StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
       Route::post('PostEditQuote/{id}', 'App\Http\Controllers\SalesController@PostEditQuote');
       Route::post('PostTransSalesOrder/{id}', 'App\Http\Controllers\SalesController@PostTransSalesOrder');
       Route::get('FilterBillQuote', 'App\Http\Controllers\SalesController@FilterBillQuote');

       //QuoteImages
       Route::get('QuoteImagesSechdule', 'App\Http\Controllers\SalesController@QuoteImagesSechdule');
       Route::get('QuoteImages', 'App\Http\Controllers\SalesController@QuoteImagesPage');
       Route::get('QuoteImagesProductFilter', 'App\Http\Controllers\SalesController@QuoteImagesProductFilter');
       Route::get('EditQuoteImages/{id}/QuoteImagesProductFilter', 'App\Http\Controllers\SalesController@QuoteImagesProductFilter');
       Route::post('AddQuoteImages', 'App\Http\Controllers\SalesController@AddQuoteImages');
       Route::get('QuoteImagePrint/{id}', 'App\Http\Controllers\SalesController@QuoteImagePrint');
       Route::get('DeleteQuoteImages/{id}', 'App\Http\Controllers\SalesController@DeleteQuoteImages');
       Route::get('EditQuoteImages/{id}', 'App\Http\Controllers\SalesController@EditQuoteImages');
       Route::post('PostEditQuoteImages/{id}', 'App\Http\Controllers\SalesController@PostEditQuoteImages');


    //Add New Client By Ajax
       Route::get('AddNewClientPOS/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClientPOS');


        Route::get('AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

       Route::get('EditQuote/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

       Route::get('HoldSale/AddNewClientPOS/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClientPOS');

       Route::get('EditSalesOrder/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');
       Route::get('ESBill/AddNewClient/{co}/{Name}/{PriceLevel}/{Phone}', 'App\Http\Controllers\SalesController@AddNewClient');

    //Sales Order
    Route::get('SalesOrderSechdule', 'App\Http\Controllers\SalesController@SalesOrderSechdule');
    Route::get('SalesOrder', 'App\Http\Controllers\SalesController@SalesOrderPage');
    Route::get('SalesOrderProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
    Route::get('AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::post('AddSalesOrder', 'App\Http\Controllers\SalesController@AddSalesOrder');
    Route::get('SalesOrderPrint/{id}', 'App\Http\Controllers\SalesController@SalesOrderPrintPage');
    Route::get('DeleteSalesOrder/{id}', 'App\Http\Controllers\SalesController@DeleteSalesOrder');
    Route::get('EditSalesOrder/{id}', 'App\Http\Controllers\SalesController@EditSalesOrder');
    Route::get('TransferToSalesSO/{id}', 'App\Http\Controllers\SalesController@TransferToSalesSO');
    Route::get('EditSalesOrder/{id}/SalesOrderProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('ESBill/{id}/SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
    Route::get('ESBill/{id}/SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
    Route::get('EditSalesOrder/UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
    Route::get('ESBill/UnitSalesOrderFilter/{id}/{Pro}/{Client}/{code}/{Store}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
    Route::get('EditSalesOrder/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('TransferToSalesExchange/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('ESBill/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
    Route::get('EditSalesOrder/StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::get('ESBill/StoreNameSalesOrderFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
    Route::post('PostEditSalesOrder/{id}', 'App\Http\Controllers\SalesController@PostEditSalesOrder');
    Route::get('FilterBillSalesOrder', 'App\Http\Controllers\SalesController@FilterBillSalesOrder');

//Sales
Route::post('PostTransSales/{id}', 'App\Http\Controllers\SalesController@PostTransSales');
Route::post('PostTransSalesExchange/{id}', 'App\Http\Controllers\SalesController@PostTransSalesExchange');
Route::get('SalesSechdule', 'App\Http\Controllers\SalesController@SalesSechdule');
Route::get('SalesSechduleTax', 'App\Http\Controllers\SalesController@SalesSechduleTax');
Route::get('SalesDeliverySechdule', 'App\Http\Controllers\SalesController@SalesDeliverySechdule');
Route::get('FilterSalesDelivery', 'App\Http\Controllers\SalesController@FilterSalesDelivery');
Route::get('MyRequestDelivery', 'App\Http\Controllers\SalesController@MyRequestDelivery');
Route::get('FilterSalesMyRequestDelivery', 'App\Http\Controllers\SalesController@FilterSalesMyRequestDelivery');
Route::get('DeliveryCollectionFilter', 'App\Http\Controllers\SalesController@DeliveryCollectionFilter');
Route::post('PostCollectDelivery', 'App\Http\Controllers\SalesController@PostCollectDelivery');
Route::post('ChangeDelivery', 'App\Http\Controllers\SalesController@ChangeDelivery');
Route::get('Sales', 'App\Http\Controllers\SalesController@SalesPage');
Route::get('SalesProductsFilter', 'App\Http\Controllers\SalesController@SalesProductsFilter');
Route::get('SalesProductsSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsSearchCodeFilter');
Route::get('UnitSalesFilter/{id}/{Pro}/{Client}/{code}', 'App\Http\Controllers\SalesController@UnitSalesFilter');
Route::get('AccountBalanceSFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
Route::get('StoreNameSalesFilter/{id}/{pro}/{code}/{Unit}/{UnitDefault}/{CodeDefault}/{StoreDefault}', 'App\Http\Controllers\SalesController@StoreNameSalesFilter');
Route::post('AddSales', 'App\Http\Controllers\SalesController@AddSales');
Route::get('SalesPrint/{id}', 'App\Http\Controllers\SalesController@SalesPrintPage');
Route::get('RecivedDelivery/{id}', 'App\Http\Controllers\SalesController@RecivedDelivery');
Route::get('SalesPrint8/{id}', 'App\Http\Controllers\SalesController@SalesPrint8Page');
Route::get('ReturnSalesPrint/{id}', 'App\Http\Controllers\SalesController@ReturnSalesPrint');
Route::get('ReturnSalesPrint8/{id}', 'App\Http\Controllers\SalesController@ReturnSalesPrint8');
Route::get('SalesPrint5/{id}', 'App\Http\Controllers\SalesController@SalesPrint5Page');
Route::get('FilterBillSales', 'App\Http\Controllers\SalesController@FilterBillSales');
Route::get('ESBill/{id}', 'App\Http\Controllers\SalesController@ESBillPage');
Route::get('DeleteSalesBill/{id}', 'App\Http\Controllers\SalesController@DeleteSalesBill');
Route::post('CollectionLaterBill', 'App\Http\Controllers\SalesController@CollectionLaterBill');
Route::post('CollectionDeliveryBill', 'App\Http\Controllers\SalesController@CollectionDeliveryBill');
Route::post('PostEditSalesBill', 'App\Http\Controllers\SalesController@PostEditSalesBill');
Route::get('SalesTakeGoods/{id}', 'App\Http\Controllers\SalesController@SalesTakeGoods');

//InstallmentSechdule
Route::get('InstallmentSechdule', 'App\Http\Controllers\SalesController@InstallmentSechdule');
Route::get('InstallBillDone/{id}', 'App\Http\Controllers\SalesController@InstallBillDone');
Route::get('UnInstallBill/{id}', 'App\Http\Controllers\SalesController@UnInstallBill');
Route::post('InstallDone', 'App\Http\Controllers\SalesController@InstallDone');
Route::get('UnInstall/{id}', 'App\Http\Controllers\SalesController@UnInstall');
Route::get('InstallClientPrint/{id}/{inst}', 'App\Http\Controllers\SalesController@InstallClientPrint');


//Sales Hold
Route::get('SalesHoldSechdule', 'App\Http\Controllers\SalesController@SalesHoldSechdule');
Route::get('RecivedSales/{id}', 'App\Http\Controllers\SalesController@RecivedSales');
Route::get('RecivedSalesProductsPrint/{id}', 'App\Http\Controllers\SalesController@RecivedSalesProductsPrint');
Route::post('PostRecivedSales/{id}', 'App\Http\Controllers\SalesController@PostRecivedSales');
Route::get('RecivedProductsSales', 'App\Http\Controllers\SalesController@RecivedProductsSales');
Route::get('FilterBillSalesHold', 'App\Http\Controllers\SalesController@FilterBillSalesHold');

//Return Sales
Route::get('ReturnSales/{id}', 'App\Http\Controllers\SalesController@ReturnSales');
Route::post('PostReturn_Sales/{id}', 'App\Http\Controllers\SalesController@PostReturn_Sales');
Route::get('ReturnSalesSechdule', 'App\Http\Controllers\SalesController@ReturnSalesSechdulePage');
Route::get('ReturnSalesSechduleTaxPage', 'App\Http\Controllers\SalesController@ReturnSalesSechduleTaxPage');

    //POS
    Route::get('POS', 'App\Http\Controllers\SalesController@POSPage');
    Route::get('POSOtherQtyFilter', 'App\Http\Controllers\SalesController@POSOtherQtyFilter');
    Route::get('HoldSale/POSOtherQtyFilter', 'App\Http\Controllers\SalesController@POSOtherQtyFilter');
    Route::post('NewShift', 'App\Http\Controllers\SalesController@NewShift');
    Route::post('CloseShift', 'App\Http\Controllers\SalesController@CloseShift');
    Route::get('SalesProductsPOSFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSFilter');
    Route::get('SalesProductsPOSSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSSearchCodeFilter');
    Route::get('SalesHoldBillsPOSFilter', 'App\Http\Controllers\SalesController@SalesHoldBillsPOSFilter');
    Route::get('HoldSale/SalesProductsPOSFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSFilter');
    Route::get('HoldSale/SalesProductsPOSSearchCodeFilter', 'App\Http\Controllers\SalesController@SalesProductsPOSSearchCodeFilter');
    Route::get('HoldSale/SalesHoldBillsPOSFilter', 'App\Http\Controllers\SalesController@SalesHoldBillsPOSFilter');
    Route::get('GroupPosFilter', 'App\Http\Controllers\SalesController@GroupPosFilter');
    Route::get('HoldSale/GroupPosFilter', 'App\Http\Controllers\SalesController@GroupPosFilter');
    Route::get('BrandsPosFilter', 'App\Http\Controllers\SalesController@BrandsPosFilter');
    Route::get('HoldSale/BrandsPosFilter', 'App\Http\Controllers\SalesController@BrandsPosFilter');
    Route::post('PostPOS', 'App\Http\Controllers\SalesController@PostPOS');
    Route::post('HoldPostPOS', 'App\Http\Controllers\SalesController@HoldPostPOS');
    Route::get('HoldSale/{id}', 'App\Http\Controllers\SalesController@HoldSale');
    Route::get('AddReciptVoucherAjax', 'App\Http\Controllers\SalesController@AddReciptVoucherAjax');
    Route::get('HoldSale/{id}/AddReciptVoucherAjax', 'App\Http\Controllers\SalesController@AddReciptVoucherAjax');
    Route::get('AddPaymentVoucherAjax', 'App\Http\Controllers\SalesController@AddPaymentVoucherAjax');
    Route::get('HoldSale/{id}/AddPaymentVoucherAjax', 'App\Http\Controllers\SalesController@AddPaymentVoucherAjax');
    Route::get('DeleteHoldBill/{id}', 'App\Http\Controllers\SalesController@DeleteHoldBill');

    Route::get('UnitsOfProducts/{id}', 'App\Http\Controllers\SalesController@UnitsOfProducts');
    Route::get('HoldSale/UnitsOfProducts/{id}', 'App\Http\Controllers\SalesController@UnitsOfProducts');
    Route::get('AllProsPosFilter', 'App\Http\Controllers\SalesController@AllProsPosFilter');
    Route::get('HoldSale/{id}/AllProsPosFilter', 'App\Http\Controllers\SalesController@AllProsPosFilter');

    Route::get('HoldSale/{id}/AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
    Route::get('HoldSale/AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
    Route::get('AllClientsAddressFilter', 'App\Http\Controllers\SalesController@AllClientsAddressFilter');
        //SalesSubscribes
              Route::get('SalesSubscribes', 'App\Http\Controllers\SalesController@SalesSubscribesPage');
              Route::get('SalesSubscribeFilter', 'App\Http\Controllers\SalesController@SalesSubscribeFilter');
    Route::get('EditSalesSubscribes/SalesSubscribeFilter', 'App\Http\Controllers\SalesController@SalesSubscribeFilter');
              Route::post('AddSalesSubscribes', 'App\Http\Controllers\SalesController@AddSalesSubscribes');
              Route::get('SalesSubscribesSechdule', 'App\Http\Controllers\SalesController@SalesSubscribesSechdule');
              Route::get('SalesSubscribesPrint/{id}', 'App\Http\Controllers\SalesController@SalesSubscribesPrint');
              Route::get('DeleteSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@DeleteSalesSubscribes');
              Route::get('EditSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@EditSalesSubscribes');

              Route::post('PostEditSalesSubscribes/{id}', 'App\Http\Controllers\SalesController@PostEditSalesSubscribes');


//TaxOnTotalTypeFilter
          Route::get('TaxOnTotalTypeFilter', 'App\Http\Controllers\SalesController@TaxOnTotalTypeFilter');
          Route::get('InstallCompanyFilter', 'App\Http\Controllers\SalesController@InstallCompanyFilter');

//StorePriceList
          Route::get('StorePriceList', 'App\Http\Controllers\SalesController@StorePriceList');
          Route::post('PriceListFilter', 'App\Http\Controllers\SalesController@PriceListFilter');

//======== End Sales ======================================================================================================

//========== CRM  =========================================================================================================

                //Governrate
                    Route::get('Governrate', 'App\Http\Controllers\CRMController@GovernratePage');
                    Route::post('AddGovernrate', 'App\Http\Controllers\CRMController@AddGovernrate');
                    Route::post('EditGovernrate/{id}', 'App\Http\Controllers\CRMController@EditGovernrate');
                    Route::get('DeleteGovernrate/{id}', 'App\Http\Controllers\CRMController@DeleteGovernrate');

                //City
                    Route::get('City/{id}', 'App\Http\Controllers\CRMController@CityPage');
                    Route::post('AddCity', 'App\Http\Controllers\CRMController@AddCity');
                    Route::post('EditCity/{id}', 'App\Http\Controllers\CRMController@EditCity');
                    Route::get('DeleteCity/{id}', 'App\Http\Controllers\CRMController@DeleteCity');

        //Places
                 Route::get('Places/{id}', 'App\Http\Controllers\CRMController@PlacesPage');
                    Route::post('AddPlaces', 'App\Http\Controllers\CRMController@AddPlaces');
                    Route::post('EditPlaces/{id}', 'App\Http\Controllers\CRMController@EditPlaces');
                    Route::get('DeletePlaces/{id}', 'App\Http\Controllers\CRMController@DeletePlaces');


                    //Activites
                    Route::get('Activites', 'App\Http\Controllers\CRMController@ActivitesPage');
                    Route::post('AddActivites', 'App\Http\Controllers\CRMController@AddActivites');
                    Route::post('EditActivites/{id}', 'App\Http\Controllers\CRMController@EditActivites');
                    Route::get('DeleteActivites/{id}', 'App\Http\Controllers\CRMController@DeleteActivites');


                    //Clients_Status
                    Route::get('Clients_Status', 'App\Http\Controllers\CRMController@Clients_StatusPage');
                    Route::post('AddClients_Status', 'App\Http\Controllers\CRMController@AddClients_Status');
                    Route::post('EditClients_Status/{id}', 'App\Http\Controllers\CRMController@EditClients_Status');
                    Route::get('DeleteClients_Status/{id}', 'App\Http\Controllers\CRMController@DeleteClients_Status');

                 //Platforms
                    Route::get('Platforms', 'App\Http\Controllers\CRMController@PlatformsPage');
                    Route::post('AddPlatforms', 'App\Http\Controllers\CRMController@AddPlatforms');
                    Route::post('EditPlatforms/{id}', 'App\Http\Controllers\CRMController@EditPlatforms');
                    Route::get('DeletePlatforms/{id}', 'App\Http\Controllers\CRMController@DeletePlatforms');

               //Campaigns
                    Route::get('Campaigns/{id}', 'App\Http\Controllers\CRMController@CampaignsPage');
                    Route::post('AddCampaigns', 'App\Http\Controllers\CRMController@AddCampaigns');
                    Route::post('EditCampaigns/{id}', 'App\Http\Controllers\CRMController@EditCampaigns');
                    Route::get('DeleteCampaigns/{id}', 'App\Http\Controllers\CRMController@DeleteCampaigns');

         //Interviews_Types
                    Route::get('Interviews_Types', 'App\Http\Controllers\CRMController@Interviews_TypesPage');
                    Route::post('AddInterviews_Types', 'App\Http\Controllers\CRMController@AddInterviews_Types');
                    Route::post('EditInterviews_Types/{id}', 'App\Http\Controllers\CRMController@EditInterviews_Types');
                    Route::get('DeleteInterviews_Types/{id}', 'App\Http\Controllers\CRMController@DeleteInterviews_Types');

            //Interviews
                    Route::get('Interviews', 'App\Http\Controllers\CRMController@InterviewsPage');
                    Route::post('AddInterviews', 'App\Http\Controllers\CRMController@AddInterviews');
                    Route::post('EditInterviews/{id}', 'App\Http\Controllers\CRMController@EditInterviews');
                    Route::get('DeleteInterviews/{id}', 'App\Http\Controllers\CRMController@DeleteInterviews');
                    Route::get('NotDone/{id}', 'App\Http\Controllers\CRMController@NotDone');
                    Route::get('Done/{id}', 'App\Http\Controllers\CRMController@Done');
                    Route::get('AllClients', 'App\Http\Controllers\CRMController@AllClients');
                    Route::get('Rate/{id}', 'App\Http\Controllers\CRMController@Rate');
                    Route::get('FilterInterviews', 'App\Http\Controllers\CRMController@FilterInterviews');
                    Route::get('AddNewClientAjax', 'App\Http\Controllers\CRMController@AddNewClientAjax');


        //Projects
         Route::get('Projects', 'App\Http\Controllers\CRMController@ProjectsPage');
         Route::post('AddProjects', 'App\Http\Controllers\CRMController@AddProjects');
         Route::post('EditProjects/{id}', 'App\Http\Controllers\CRMController@EditProjects');
         Route::get('DeleteProjects/{id}', 'App\Http\Controllers\CRMController@DeleteProjects');
         Route::get('EndProject/{id}', 'App\Http\Controllers\CRMController@EndProject');

          //Missions
         Route::get('MyMissions', 'App\Http\Controllers\CRMController@MyMissions');
         Route::get('Missions', 'App\Http\Controllers\CRMController@MissionsPage');
         Route::post('AddMissions', 'App\Http\Controllers\CRMController@AddMissions');
         Route::post('EditMissions/{id}', 'App\Http\Controllers\CRMController@EditMissions');
         Route::get('DeleteMissions/{id}', 'App\Http\Controllers\CRMController@DeleteMissions');
         Route::get('SureMission/{id}', 'App\Http\Controllers\CRMController@SureMission');
         Route::get('SureMissionByEmp/{id}', 'App\Http\Controllers\CRMController@SureMissionByEmp');
         Route::post('AcceptMission', 'App\Http\Controllers\CRMController@AcceptMission');

        //PerivousMettings
           Route::get('PerivousMettings', 'App\Http\Controllers\CRMController@PerivousMettings');
           Route::post('AddNEWInterviews', 'App\Http\Controllers\CRMController@AddNEWInterviews');



    //MyMettings
       Route::get('MyMettings', 'App\Http\Controllers\CRMController@MyMettingsPage');



//=====  End CRM ==========================================================================================================


//=============== Reports =================================================================================================

    //Product Informations
        Route::get('Product_Info', 'App\Http\Controllers\ReportController@Product_InfoPage');
        Route::get('ProductInfoFilter', 'App\Http\Controllers\ReportController@ProductInfoFilter');


    //ProductOrderLimit
    Route::get('ProductOrderLimit', 'App\Http\Controllers\ReportController@ProductOrderLimitPage');
    Route::get('ProductOrderLimitFilter', 'App\Http\Controllers\ReportController@ProductOrderLimitFilter');
    Route::get('ProductOrderLimitFilterTwo', 'App\Http\Controllers\ReportController@ProductOrderLimitFilterTwo');

     //Report Start Period
    Route::get('ReportStartPeriod', 'App\Http\Controllers\ReportController@ReportStartPeriodPage');
    Route::get('ReportProductStartFilter', 'App\Http\Controllers\ReportController@ReportProductStartFilter');
    Route::get('ReportStartPeriodProductsFilterTwo', 'App\Http\Controllers\ReportController@ReportStartPeriodProductsFilterTwo');


    //SettlementsReports
       Route::get('SettlementsReports', 'App\Http\Controllers\ReportController@SettlementsReportsPage');
       Route::get('ReportProductSettlementFilter', 'App\Http\Controllers\ReportController@ReportProductSettlementFilter');
       Route::get('SettlementsReportsFilterTwo', 'App\Http\Controllers\ReportController@SettlementsReportsFilterTwo');

    //StoresCost
    Route::get('StoresCost', 'App\Http\Controllers\ReportController@StoresCostPage');
    Route::get('StoresCostFilter', 'App\Http\Controllers\ReportController@StoresCostFilter');
    Route::get('StoresCostFilterTwo', 'App\Http\Controllers\ReportController@StoresCostFilterTwo');

            //CreditStores
    Route::get('CreditStores', 'App\Http\Controllers\ReportController@CreditStores');
    Route::get('CreditStoresFilterTwo', 'App\Http\Controllers\ReportController@CreditStoresFilterTwo');



        //StoresInventory
    Route::get('StoresInventory', 'App\Http\Controllers\ReportController@StoresInventoryPage');
    Route::get('StoresInventoryFilter', 'App\Http\Controllers\ReportController@StoresInventoryFilter');
    Route::get('StoresInventoryFilterTwo', 'App\Http\Controllers\ReportController@StoresInventoryFilterTwo');


    //StagnantItems
       Route::get('StagnantItems', 'App\Http\Controllers\ReportController@StagnantItemsPage');
       Route::get('StegnantProductsFilter', 'App\Http\Controllers\ReportController@StegnantProductsFilter');
       Route::get('StagnantItemsFilterTwo', 'App\Http\Controllers\ReportController@StagnantItemsFilterTwo');


    //ItemsMoves
       Route::get('ItemsMoves', 'App\Http\Controllers\ReportController@ItemsMovesPage');
       Route::get('ProductsMovesFilter', 'App\Http\Controllers\ReportController@ProductsMovesFilter');
       Route::get('ItemsMovesFilterTwo', 'App\Http\Controllers\ReportController@ItemsMovesFilterTwo');

    //TotalExpensesSafes
         Route::get('TotalExpensesSafes', 'App\Http\Controllers\ReportController@TotalExpensesSafes');
       Route::get('TotalExpensesSafesFilter', 'App\Http\Controllers\ReportController@TotalExpensesSafesFilter');


    //StoresBalances
       Route::get('StoresBalances', 'App\Http\Controllers\ReportController@StoresBalancesPage');
       Route::get('StoresBalanceFilter', 'App\Http\Controllers\ReportController@StoresBalanceFilter');
       Route::get('StoresBalancesFilterTwo', 'App\Http\Controllers\ReportController@StoresBalancesFilterTwo');


    //NetPurchases
       Route::get('NetPurchases', 'App\Http\Controllers\ReportController@NetPurchasesPage');
       Route::get('NetPurchasesFilter', 'App\Http\Controllers\ReportController@NetPurchasesFilter');
       Route::get('NetPurchasesFilterTwo', 'App\Http\Controllers\ReportController@NetPurchasesFilterTwo');

    //NetSales
      Route::get('NetSales', 'App\Http\Controllers\ReportController@NetSalesPage');
      Route::get('NetSalesFilter', 'App\Http\Controllers\ReportController@NetSalesFilter');
      Route::get('NetSalesFilterTwo', 'App\Http\Controllers\ReportController@NetSalesFilterTwo');

    //TotalNetPurchases
      Route::get('TotalNetPurchases', 'App\Http\Controllers\ReportController@TotalNetPurchasesPage');
      Route::get('TotalNetPurchasesFilter', 'App\Http\Controllers\ReportController@TotalNetPurchasesFilter');
      Route::get('TotalNetPurchasesFilterTwo', 'App\Http\Controllers\ReportController@TotalNetPurchasesFilterTwo');

    //TotalNetSales
     Route::get('TotalNetSales', 'App\Http\Controllers\ReportController@TotalNetSalesPage');
      Route::get('TotalNetSalesFilter', 'App\Http\Controllers\ReportController@TotalNetSalesFilter');
      Route::get('TotalNetSalesFilterTwo', 'App\Http\Controllers\ReportController@TotalNetSalesFilterTwo');

    //Profits
       Route::get('Profits', 'App\Http\Controllers\ReportController@ProfitsPage');
       Route::get('ProfitsFilterrr', 'App\Http\Controllers\ReportController@ProfitsFilter');
       Route::get('ProfitsFilterTwo', 'App\Http\Controllers\ReportController@ProfitsFilterTwo');


            //ProductProfits
       Route::get('ProductProfits', 'App\Http\Controllers\ReportController@ProductProfits');
       Route::get('ProductProfitsFilter', 'App\Http\Controllers\ReportController@ProductProfitsFilter');
     Route::get('ProductsFilterTwo', 'App\Http\Controllers\ReportController@ProductsFilterTwo');

        //ProductProfitsNew
            Route::get('ProductProfitsNew', 'App\Http\Controllers\ReportController@ProductProfitsNew');
       Route::get('ProductProfitsNewFilter', 'App\Http\Controllers\ReportController@ProductProfitsNewFilter');
               Route::get('ExceptProductProfitsNewFilterTwo', 'App\Http\Controllers\ReportController@ExceptProductProfitsNewFilterTwo');



        //ShiftsReport
       Route::get('ShiftsReport', 'App\Http\Controllers\ReportController@ShiftsReportPage');
       Route::get('ShiftReportFilter', 'App\Http\Controllers\ReportController@ShiftReportFilter');

        //ShiftsDetailsReport
       Route::get('ShiftsDetailsReport', 'App\Http\Controllers\ReportController@ShiftsDetailsReportPage');
       Route::get('FilterShiftDetails', 'App\Http\Controllers\ReportController@FilterShiftDetails');

        //DailyClosing
       Route::get('DailyClosing', 'App\Http\Controllers\ReportController@DailyClosingPage');
       Route::get('FilterDailyClosing', 'App\Http\Controllers\ReportController@FilterDailyClosing');
       Route::get('FilterDailyClosingForm', 'App\Http\Controllers\ReportController@FilterDailyClosingForm');

        //ProductsReports
            Route::get('ProductsReports', 'App\Http\Controllers\ReportController@ProductsReportsPage');
            Route::get('ProductsReportFilter', 'App\Http\Controllers\ReportController@ProductsReportFilter');
      Route::get('ExpensesReportFilterTwo', 'App\Http\Controllers\ReportController@ExpensesReportFilterTwo');
       Route::get('EmployeeCommissionDiscountsFilterTwo', 'App\Http\Controllers\ReportController@EmployeeCommissionDiscountsFilterTwo');
       Route::get('ExceptProfitsFilterTwo', 'App\Http\Controllers\ReportController@ExceptProfitsFilterTwo');
       Route::get('ExceptProductProfitsFilterTwo', 'App\Http\Controllers\ReportController@ExceptProductProfitsFilterTwo');
//DailyShifts
          Route::get('DailyShifts', 'App\Http\Controllers\ReportController@DailyShiftsPage');
          Route::get('DailyShiftsFilter', 'App\Http\Controllers\ReportController@DailyShiftsFilter');

          //ExpensesReport
          Route::get('ExpensesReport', 'App\Http\Controllers\ReportController@ExpensesReportPage');
          Route::get('FilterExpensesReport', 'App\Http\Controllers\ReportController@FilterExpensesReport');

          //DailyProducts
          Route::get('DailyProducts', 'App\Http\Controllers\ReportController@DailyProductsPage');
          Route::get('DailyProductsFilter', 'App\Http\Controllers\ReportController@DailyProductsFilter');

          //EmployeeCommissionDiscounts
          Route::get('EmployeeCommissionDiscounts', 'App\Http\Controllers\ReportController@EmployeeCommissionDiscountsPage');
          Route::get('EmpCommisionFilter', 'App\Http\Controllers\ReportController@EmpCommisionFilter');

          //VendorPricesReport
          Route::get('VendorPricesReport', 'App\Http\Controllers\ReportController@VendorPricesReportPage');
          Route::get('VendorPricesReportFilter', 'App\Http\Controllers\ReportController@VendorPricesReportFilter');

          //GroupsSales
          Route::get('GroupsSales', 'App\Http\Controllers\ReportController@GroupsSalesPage');
          Route::get('GroupsSalesFilter', 'App\Http\Controllers\ReportController@GroupsSalesFilter');


          //Collection_Delegates
          Route::get('Collection_Delegates', 'App\Http\Controllers\ReportController@Collection_DelegatesPage');
          Route::get('FilterCollection_Delegates', 'App\Http\Controllers\ReportController@FilterCollection_Delegates');

          //Sales_Delegates
          Route::get('Sales_Delegates', 'App\Http\Controllers\ReportController@Sales_DelegatesPage');
          Route::get('FilterSales_Delegates', 'App\Http\Controllers\ReportController@FilterSales_Delegates');
          Route::get('Sales_DelegatesFilterTwo', 'App\Http\Controllers\ReportController@Sales_DelegatesFilterTwo');

        //ClientSales
                 Route::get('ClientSales', 'App\Http\Controllers\ReportController@ClientSalesPage');
                 Route::get('ClientSalesFilter', 'App\Http\Controllers\ReportController@ClientSalesFilter');
                 Route::get('ClientSalesFilterTwo', 'App\Http\Controllers\ReportController@ClientSalesFilterTwo');

          //DelegateSalesDetails
                 Route::get('DelegateSalesDetails', 'App\Http\Controllers\ReportController@DelegateSalesDetails');
                 Route::get('DelegateSalesDetailsFilter', 'App\Http\Controllers\ReportController@DelegateSalesDetailsFilter');


          //StoresSalesDetails
                 Route::get('StoresSalesDetails', 'App\Http\Controllers\ReportController@StoresSalesDetails');
                 Route::get('StoresSalesDetailsFilter', 'App\Http\Controllers\ReportController@StoresSalesDetailsFilter');


                 //DailyMoves
                 Route::get('DailyMoves', 'App\Http\Controllers\ReportController@DailyMovesPage');
                 Route::get('DailyMovesFilter', 'App\Http\Controllers\ReportController@DailyMovesFilter');

                 //VendorPurchases
                 Route::get('VendorPurchases', 'App\Http\Controllers\ReportController@VendorPurchasesPage');
                 Route::get('VendorPurchasesFilter', 'App\Http\Controllers\ReportController@VendorPurchasesFilter');

                 //ExecutorSales
                 Route::get('ExecutorSales', 'App\Http\Controllers\ReportController@ExecutorSales');
                 Route::get('ExecutorSalesFilter', 'App\Http\Controllers\ReportController@ExecutorSalesFilter');

                 //InstallmentReport
                 Route::get('InstallmentReport', 'App\Http\Controllers\ReportController@InstallmentReport');
                 Route::get('InstallmentReportFilter', 'App\Http\Controllers\ReportController@InstallmentReportFilter');
                 Route::get('InstallmentReportFilterTwo', 'App\Http\Controllers\ReportController@InstallmentReportFilterTwo');



                 //ExceptProfits
                 Route::get('ExceptProfits', 'App\Http\Controllers\ReportController@ExceptProfits');
                 Route::get('ExceptProfitsFilter', 'App\Http\Controllers\ReportController@ExceptProfitsFilter');

                 //ExpiredProucts
                 Route::get('ExpiredProucts', 'App\Http\Controllers\ReportController@ExpiredProucts');
                 Route::get('ExpiredProuctsFilter', 'App\Http\Controllers\ReportController@ExpiredProuctsFilter');
                 Route::get('ExpiredProuctsFilterTwo', 'App\Http\Controllers\ReportController@ExpiredProuctsFilterTwo');

        //TotalDailyMoves
               Route::get('TotalDailyMoves', 'App\Http\Controllers\ReportController@TotalDailyMoves');
                 Route::get('TotalDailyMovesFilter', 'App\Http\Controllers\ReportController@TotalDailyMovesFilter');



        //New Reports =========================================

           //SalesBills
            Route::get('SalesBills', 'App\Http\Controllers\NewReportController@SalesBills');
            Route::get('SalesBillFilter', 'App\Http\Controllers\NewReportController@SalesBillFilter');
            Route::get('SaveDefaultColumnsSalesBills', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsSalesBills');
            Route::get('SalesBillsReportPrint/{type}', 'App\Http\Controllers\NewReportController@SalesBillsReportPrint');

        //PurchasesBills
            Route::get('PurchasesBills', 'App\Http\Controllers\NewReportController@PurchasesBills');
            Route::get('PurchasesBillsFilter', 'App\Http\Controllers\NewReportController@PurchasesBillsFilter');
            Route::get('SaveDefaultColumnsPurchasesBills', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsPurchasesBills');
            Route::get('PurchasesBillsReportPrint/{type}', 'App\Http\Controllers\NewReportController@PurchasesBillsReportPrint');


        //StoresTransferReport
             Route::get('StoresTransferReport', 'App\Http\Controllers\NewReportController@StoresTransferReport');
           Route::get('StoresTransferReportFilter', 'App\Http\Controllers\NewReportController@StoresTransferReportFilter');
            Route::get('SaveDefaultColumnsStoresTransferReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsStoresTransferReport');
            Route::get('StoresTransferReportPrint', 'App\Http\Controllers\NewReportController@StoresTransferReportPrint');

        //SafesTransferReport
            Route::get('SafesTransferReport', 'App\Http\Controllers\NewReportController@SafesTransferReport');
           Route::get('SafesTransferReportFilter', 'App\Http\Controllers\NewReportController@SafesTransferReportFilter');
            Route::get('SaveDefaultColumnsSafesTransferReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsSafesTransferReport');
            Route::get('SafesTransferReportPrint', 'App\Http\Controllers\NewReportController@SafesTransferReportPrint');


        //StoresMovesReport
         Route::get('StoresMovesReport', 'App\Http\Controllers\NewReportController@StoresMovesReport');
           Route::get('StoresMovesReportFilter', 'App\Http\Controllers\NewReportController@StoresMovesReportFilter');
            Route::get('SaveDefaultColumnsStoresMovesReport', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsStoresMovesReport');
            Route::get('StoresMovesReportPrint', 'App\Http\Controllers\NewReportController@StoresMovesReportPrint');


        //ProductMoveDetails
                Route::get('ProductMoveDetails', 'App\Http\Controllers\NewReportController@ProductMoveDetails');
             Route::get('ProductMoveDetailsFilter', 'App\Http\Controllers\NewReportController@ProductMoveDetailsFilter');
            Route::get('SaveDefaultColumnsProductMoveDetails', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsProductMoveDetails');
            Route::get('ProductMoveDetailsPrint', 'App\Http\Controllers\NewReportController@ProductMoveDetailsPrint');


        //CompareSalesPrice
            Route::get('CompareSalesPrice', 'App\Http\Controllers\NewReportController@CompareSalesPrice');
            Route::get('CompareSalesPriceFilter', 'App\Http\Controllers\NewReportController@CompareSalesPriceFilter');
            Route::get('SaveDefaultColumnsCompareSalesPrice', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsCompareSalesPrice');
            Route::get('CompareSalesPricePrint', 'App\Http\Controllers\NewReportController@CompareSalesPricePrint');


                //ProfitSalesProduct
            Route::get('ProfitSalesProduct', 'App\Http\Controllers\NewReportController@ProfitSalesProduct');
            Route::get('ProfitSalesProductFilter', 'App\Http\Controllers\NewReportController@ProfitSalesProductFilter');
            Route::get('SaveDefaultColumnsProfitSalesProduct', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsProfitSalesProduct');
            Route::get('ProfitSalesProductPrint', 'App\Http\Controllers\NewReportController@ProfitSalesProductPrint');

            //ClientAccountStatement
         Route::get('ClientAccountStatement', 'App\Http\Controllers\NewReportController@ClientAccountStatement');
          Route::get('ClientAccountStatementFilter', 'App\Http\Controllers\NewReportController@ClientAccountStatementFilter');
             Route::get('ClientAccountStatement/SaveDefaultClientStatement', 'App\Http\Controllers\NewReportController@SaveDefaultClientStatement');
             Route::get('SaveDefaultClientStatement', 'App\Http\Controllers\NewReportController@SaveDefaultClientStatement');
            Route::get('ClientAccountStatementPrint', 'App\Http\Controllers\NewReportController@ClientAccountStatementPrint');

        //MostSalesProducts
           Route::get('MostSalesProducts', 'App\Http\Controllers\NewReportController@MostSalesProducts');
            Route::get('MostSalesProductsFilter', 'App\Http\Controllers\NewReportController@MostSalesProductsFilter');
            Route::get('SaveDefaultColumnsMostSalesProducts', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsMostSalesProducts');
            Route::get('MostSalesProductsPrint', 'App\Http\Controllers\NewReportController@MostSalesProductsPrint');


        //VendorAccountStatement
          Route::get('VendorAccountStatement', 'App\Http\Controllers\NewReportController@VendorAccountStatement');
          Route::get('VendorAccountStatementFilter', 'App\Http\Controllers\NewReportController@VendorAccountStatementFilter');
            Route::get('SaveDefaultColumnsVendorAccountStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsVendorAccountStatement');
            Route::get('VendorAccountStatementPrint', 'App\Http\Controllers\NewReportController@VendorAccountStatementPrint');



          //ClientsStatement
           Route::get('ClientsStatement', 'App\Http\Controllers\NewReportController@ClientsStatement');
          Route::get('ClientsStatementFilter', 'App\Http\Controllers\NewReportController@ClientsStatementFilter');
            Route::get('SaveDefaultColumnsClientsStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsClientsStatement');
            Route::get('ClientsStatementPrint', 'App\Http\Controllers\NewReportController@ClientsStatementPrint');

          //VendorsStatement
            Route::get('VendorsStatement', 'App\Http\Controllers\NewReportController@VendorsStatement');
          Route::get('VendorsStatementFilter', 'App\Http\Controllers\NewReportController@VendorsStatementFilter');
            Route::get('SaveDefaultColumnsVendorsStatement', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsVendorsStatement');
            Route::get('VendorsStatementPrint', 'App\Http\Controllers\NewReportController@VendorsStatementPrint');


        //ExpensesList
            Route::get('ExpensesList', 'App\Http\Controllers\NewReportController@ExpensesList');
            Route::get('ExpensesListFilter', 'App\Http\Controllers\NewReportController@ExpensesListFilter');
            Route::get('SaveDefaultColumnsExpensesList', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsExpensesList');
            Route::get('ExpensesListPrint', 'App\Http\Controllers\NewReportController@ExpensesListPrint');


             //StoresCostNew
        Route::get('StoresCostNew', 'App\Http\Controllers\NewReportController@StoresCostNew');
            Route::get('StoresCostNewFilter', 'App\Http\Controllers\NewReportController@StoresCostNewFilter');
  Route::get('StoresCostNewPrint', 'App\Http\Controllers\NewReportController@StoresCostNewPrint');


        //SubIncomList
            Route::get('SubIncomList', 'App\Http\Controllers\NewReportController@SubIncomList');
            Route::get('SubIncomListFilter', 'App\Http\Controllers\NewReportController@SubIncomListFilter');

      //EmpGoals
            Route::get('EmpGoals', 'App\Http\Controllers\NewReportController@EmpGoals');
    Route::get('EmpGoalsFilter', 'App\Http\Controllers\NewReportController@EmpGoalsFilter');

        //InventorySerial
              Route::get('InventorySerial', 'App\Http\Controllers\NewReportController@InventorySerial');
    Route::get('InventorySerialFilter', 'App\Http\Controllers\NewReportController@InventorySerialFilter');


        //StoresBalancesNew
              Route::get('StoresBalancesNew', 'App\Http\Controllers\NewReportController@StoresBalancesNew');
            Route::get('StoresBalancesNewFilter', 'App\Http\Controllers\NewReportController@StoresBalancesNewFilter');
  Route::get('StoresBalancesNewPrint', 'App\Http\Controllers\NewReportController@StoresBalancesNewPrint');

        //StoresBalancesCat
                     Route::get('StoresBalancesCatNew', 'App\Http\Controllers\NewReportController@StoresBalancesCat');
            Route::get('StoresBalancesCatFilter', 'App\Http\Controllers\NewReportController@StoresBalancesCatFilter');
  Route::get('StoresBalancesCatPrint', 'App\Http\Controllers\NewReportController@StoresBalancesCatPrint');


        //StoresInventoryNew
                             Route::get('StoresInventoryNew', 'App\Http\Controllers\NewReportController@StoresInventoryNew');
            Route::get('StoresInventoryNewFilter', 'App\Http\Controllers\NewReportController@StoresInventoryNewFilter');
  Route::get('StoresInventoryNewPrint', 'App\Http\Controllers\NewReportController@StoresInventoryNewPrint');


        //ItemCost
                 Route::get('ItemCost', 'App\Http\Controllers\NewReportController@ItemCost');
            Route::get('ItemCostNewFilter', 'App\Http\Controllers\NewReportController@ItemCostNewFilter');


        //DelegateSalesDetails
            Route::get('DelegateSalesDetailss', 'App\Http\Controllers\NewReportController@DelegateSalesDetailss');
            Route::get('DelegateSalesDetailssFilter', 'App\Http\Controllers\NewReportController@DelegateSalesDetailssFilter');
            Route::get('DelegateSalesDetailssFilterPrint', 'App\Http\Controllers\NewReportController@DelegateSalesDetailssFilterPrint');

           //ProfitDelegateSalesDetails
            Route::get('ProfitDelegateSalesDetails', 'App\Http\Controllers\NewReportController@ProfitDelegateSalesDetails');
            Route::get('ProfitDelegateSalesDetailsFilter', 'App\Http\Controllers\NewReportController@ProfitDelegateSalesDetailsFilter');


            //StoresCosts
            Route::get('StoresCosts', 'App\Http\Controllers\NewReportController@StoresCosts');
            Route::get('StoresCostsFilter', 'App\Http\Controllers\NewReportController@StoresCostsFilter');



                //DailyClosingDetails
            Route::get('DailyClosingDetails', 'App\Http\Controllers\NewReportController@DailyClosingDetails');
            Route::get('DailyClosingDetailsFilter', 'App\Http\Controllers\NewReportController@DailyClosingDetailsFilter');
            Route::get('DailyClosingDetailsFilterPrint', 'App\Http\Controllers\NewReportController@DailyClosingDetailsFilterPrint');


        //InstallmentCompaniesSales

            Route::get('InstallmentCompaniesSales', 'App\Http\Controllers\NewReportController@InstallmentCompaniesSales');
          Route::get('InstallmentCompaniesSalesFilter', 'App\Http\Controllers\NewReportController@InstallmentCompaniesSalesFilter');
            Route::get('SaveDefaultColumnsInstallmentCompaniesSales', 'App\Http\Controllers\NewReportController@SaveDefaultColumnsInstallmentCompaniesSales');


        //SalesProsMoreDetails
               Route::get('SalesProsMoreDetails', 'App\Http\Controllers\NewReportController@SalesProsMoreDetails');
               Route::get('SalesProsMoreDetailsFilter', 'App\Http\Controllers\NewReportController@SalesProsMoreDetailsFilter');

        //StagnantItemsTwo
  Route::get('StagnantItemsTwo', 'App\Http\Controllers\NewReportController@StagnantItemsTwo');
               Route::get('StagnantItemsTwoFilter', 'App\Http\Controllers\NewReportController@StagnantItemsTwoFilter');

        //SalesCustomersGroups
         Route::get('SalesCustomersGroups', 'App\Http\Controllers\NewReportController@SalesCustomersGroups');
         Route::get('SalesCustomersGroupsFilter', 'App\Http\Controllers\NewReportController@SalesCustomersGroupsFilter');

        //Filters
          Route::get('BranchReportStoresFilter', 'App\Http\Controllers\NewReportController@BranchReportStoresFilter');
          Route::get('BranchReportSafesFilter', 'App\Http\Controllers\NewReportController@BranchReportSafesFilter');
          Route::get('ClientGroupsFilter', 'App\Http\Controllers\NewReportController@ClientGroupsFilter');
          Route::get('GroupSingleFilter', 'App\Http\Controllers\NewReportController@GroupSingleFilter');
          Route::get('SubAccountFilter', 'App\Http\Controllers\NewReportController@SubAccountFilter');



        //===========================================================







//===== End Reports =======================================================================================================


//===== Maintenance =======================================================================================================

                 //Manufactur Company
                 Route::get('ManufacturCompany', 'App\Http\Controllers\MaintenanceController@ManufacturCompanyPage');
                 Route::post('AddManufacturCompany', 'App\Http\Controllers\MaintenanceController@AddManufacturCompany');
                 Route::post('EditManufacturCompany/{id}', 'App\Http\Controllers\MaintenanceController@EditManufacturCompany');
                 Route::get('DeleteManufacturCompany/{id}', 'App\Http\Controllers\MaintenanceController@DeleteManufacturCompany');


                 //Devices Types
                 Route::get('DevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@DevicesTypesPage');
                 Route::post('AddDevicesTypes', 'App\Http\Controllers\MaintenanceController@AddDevicesTypes');
                 Route::post('EditDevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@EditDevicesTypes');
                 Route::get('DeleteDevicesTypes/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDevicesTypes');

                 //Desvice Cases
                 Route::get('DesviceCases', 'App\Http\Controllers\MaintenanceController@DesviceCasesPage');
                 Route::post('AddDesviceCases', 'App\Http\Controllers\MaintenanceController@AddDesviceCases');
                 Route::post('EditDesviceCases/{id}', 'App\Http\Controllers\MaintenanceController@EditDesviceCases');
                 Route::get('DeleteDesviceCases/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDesviceCases');

                 //RefuseReasons
                 Route::get('RefuseReasons', 'App\Http\Controllers\MaintenanceController@RefuseReasonsPage');
                 Route::post('AddRefuseReasons', 'App\Http\Controllers\MaintenanceController@AddRefuseReasons');
                 Route::post('EditRefuseReasons/{id}', 'App\Http\Controllers\MaintenanceController@EditRefuseReasons');
                 Route::get('DeleteRefuseReasons/{id}', 'App\Http\Controllers\MaintenanceController@DeleteRefuseReasons');

                 //Faults Type
                 Route::get('FaultsType', 'App\Http\Controllers\MaintenanceController@FaultsTypePage');
                 Route::post('AddFaultsType', 'App\Http\Controllers\MaintenanceController@AddFaultsType');
                 Route::post('EditFaultsType/{id}', 'App\Http\Controllers\MaintenanceController@EditFaultsType');
                 Route::get('DeleteFaultsType/{id}', 'App\Http\Controllers\MaintenanceController@DeleteFaultsType');
    //ReciptMaintaince
Route::get('ReciptMaintaince', 'App\Http\Controllers\MaintenanceController@ReciptMaintaincePage');
Route::get('FilterReciptBills', 'App\Http\Controllers\MaintenanceController@FilterReciptBills');
Route::get('FilterMainBills', 'App\Http\Controllers\MaintenanceController@FilterMainBills');
Route::get('CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::post('AddReciptMaintaince', 'App\Http\Controllers\MaintenanceController@AddReciptMaintaince');
Route::get('ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('ReciptMaintaincePrint/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintaincePrint');
Route::get('ReciptMaintainceSechdule', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceSechdulePage');
Route::get('EditReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@EditReciptMaintaince');
Route::get('DeleteReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@DeleteReciptMaintaince');
Route::post('PostEditReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@PostEditReciptMaintaince');
Route::get('EditReciptMaintaince/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('EditReciptMaintaince/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('EditReciptMaintaince/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('EditReciptMaintaince/{id}/OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
Route::get('SureReciptMain/{id}', 'App\Http\Controllers\MaintenanceController@SureReciptMain');
Route::get('RefuseReciptMain/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMain');
Route::post('RefuseReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMaintaince');
Route::post('TransferReciptMaintaince/{id}', 'App\Http\Controllers\MaintenanceController@TransferReciptMaintaince');
Route::get('ReciptMaintainceSechduleEng', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceSechduleEng');
Route::get('ReportClientMain/{id}', 'App\Http\Controllers\MaintenanceController@ReportClientMain');
Route::post('RefuseReciptMaintainceClient/{id}', 'App\Http\Controllers\MaintenanceController@RefuseReciptMaintainceClient');
Route::get('SureMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@SureMaintainceBill'); Route::post('PostSureMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostSureMaintainceBill');
Route::get('OpenMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@OpenMaintainceBill');
Route::get('EditReciptMaintainceEng/{id}', 'App\Http\Controllers\MaintenanceController@EditReciptMaintainceEng');
Route::get('SureMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('SureMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('SureMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('OpenMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('OpenMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('OpenMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('ReciptMaintanceTalf/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');

Route::post('PostOpenMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostOpenMaintainceBill');
Route::get('EditReciptMaintainceEng/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('EditReciptMaintainceEng/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('EditReciptMaintainceEng/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('EditReciptMaintainceEng/{id}/OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
Route::post('PostEditReciptMaintainceEng/{id}', 'App\Http\Controllers\MaintenanceController@PostEditReciptMaintainceEng');
Route::get('ReturnMaintainceBill/CompanyManufactureFilter/{id}', 'App\Http\Controllers\MaintenanceController@CompanyManufactureFilter');
Route::get('ReturnMaintainceBill/ErrorNamrFilter/{id}', 'App\Http\Controllers\MaintenanceController@ErrorNamrFilter');
Route::get('ReturnMaintainceBill/{id}/MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
Route::get('ReturnMaintainceBill/{id}/AvaliableQtyMaintanceFilter', 'App\Http\Controllers\MaintenanceController@AvaliableQtyMaintanceFilter');
Route::post('SureMaintainceBillForm/{id}', 'App\Http\Controllers\MaintenanceController@SureMaintainceBillForm');
Route::get('ReciptMaintanceTalf/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintanceTalf');
Route::post('PostSureMaintainceBillTalf', 'App\Http\Controllers\MaintenanceController@PostSureMaintainceBillTalf');
    //Return Maintaince
Route::get('ReturnMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@ReturnMaintainceBillPage');
Route::post('PostReturnMaintainceBill/{id}', 'App\Http\Controllers\MaintenanceController@PostReturnMaintainceBill');
Route::get('ReturnMaintainceBillSechdule', 'App\Http\Controllers\MaintenanceController@ReturnMaintainceBillSechdule');
Route::get('ReturnMaintaincePrint/{id}', 'App\Http\Controllers\MaintenanceController@ReturnMaintaincePrint');
Route::get('FilterReturnMainBills', 'App\Http\Controllers\MaintenanceController@FilterReturnMainBills');



    //MaintainceBill
    Route::get('MaintainceBill', 'App\Http\Controllers\MaintenanceController@MaintainceBillPage');
    Route::get('ErrorsFilter', 'App\Http\Controllers\MaintenanceController@ErrorsFilter');
    Route::get('ReciptMaintainceNumberFilter/{id}', 'App\Http\Controllers\MaintenanceController@ReciptMaintainceNumberFilter');
    Route::get('MaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@MaintainceProductsFilter');
    Route::get('NewMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@NewMaintainceProductsFilter');
    Route::get('OldMaintainceProductsFilter', 'App\Http\Controllers\MaintenanceController@OldMaintainceProductsFilter');
    Route::post('AddMaintainceBill', 'App\Http\Controllers\MaintenanceController@AddMaintainceBill');
    Route::get('MaintenanceBillPrint/{id}', 'App\Http\Controllers\MaintenanceController@MaintenanceBillPrint');
    Route::get('MaintainceBillSechdule', 'App\Http\Controllers\MaintenanceController@MaintainceBillSechdule');

                       //Device Descrips
                Route::get('DeviceDescrips', 'App\Http\Controllers\MaintenanceController@DeviceDescripsPage');
                Route::post('AddDeviceDescrips', 'App\Http\Controllers\MaintenanceController@AddDeviceDescrips');
                Route::post('EditDeviceDescrips/{id}', 'App\Http\Controllers\MaintenanceController@EditDeviceDescrips');
                Route::get('DeleteDeviceDescrips/{id}', 'App\Http\Controllers\MaintenanceController@DeleteDeviceDescrips');

                //Terms Maintaince
                Route::get('TermsMaintaince', 'App\Http\Controllers\MaintenanceController@TermsMaintaincePage');
                Route::post('AddTermsMaintaince', 'App\Http\Controllers\MaintenanceController@AddTermsMaintaince');


                //MaintainceColors
                Route::get('MaintainceColors', 'App\Http\Controllers\MaintenanceController@MaintainceColorsPage');
                Route::post('AddMaintainceColors', 'App\Http\Controllers\MaintenanceController@AddMaintainceColors');

 //=====  End Maintenance ===================================================================================================


//=====  Manufacturing ===================================================================================================


    //Manufacturing Halls
    Route::get('ManufacturingHalls', 'App\Http\Controllers\ManufacturingController@ManufacturingHallsPage');
    Route::post('AddManufacturingHalls', 'App\Http\Controllers\ManufacturingController@AddManufacturingHalls');
    Route::post('EditManufacturingHalls/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingHalls');
    Route::get('DeleteManufacturingHalls/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingHalls');

    //ManufacturingModel
       Route::get('ManufacturingModel', 'App\Http\Controllers\ManufacturingController@ManufacturingModelPage');
       Route::post('AddManufacturingModel', 'App\Http\Controllers\ManufacturingController@AddManufacturingModel');
       Route::get('IncomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilter');
       Route::get('OutcomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilter');

       Route::get('EditManufacturingModel/IncomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilter');
       Route::get('EditManufacturingModel/OutcomManufacturingProductsFilter', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilter');

       Route::get('ManuExecution/{id}', 'App\Http\Controllers\ManufacturingController@ManuExecutionPage');
       Route::get('PrintManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@PrintManufacturingModel');



       //Sechdule
       Route::get('ManufacturingModelSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingModelSechdule');
       Route::get('EditManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingModel');
       Route::get('EditManufacturingModelPrecent/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingModelPrecent');
       Route::get('DeleteManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingModel');
       Route::post('PostEditManufacturingModel/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingModel');
       Route::post('PostEditManufacturingModelPrecent/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingModelPrecent');


       //ManufacturingModelPrecent
       Route::get('ManufacturingModelPrecent', 'App\Http\Controllers\ManufacturingController@ManufacturingModelPrecentPage');
       Route::get('OutcomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilterPrecent');
       Route::get('EditManufacturingModelPrecent/OutcomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@OutcomManufacturingProductsFilterPrecent');
       Route::get('IncomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilterPrecent');
       Route::get('EditManufacturingModelPrecent/IncomManufacturingProductsFilterPrecent', 'App\Http\Controllers\ManufacturingController@IncomManufacturingProductsFilterPrecent');
       Route::post('AddManufacturingModelPrecent', 'App\Http\Controllers\ManufacturingController@AddManufacturingModelPrecent');


       //ExecutingandReceiving
       Route::get('ExecutingandReceiving', 'App\Http\Controllers\ManufacturingController@ExecutingandReceiving');
       Route::get('ModelExecutingFilter', 'App\Http\Controllers\ManufacturingController@ModelExecutingFilter');
       Route::get('ManuExecution/{id}/ModelExecutingFilter', 'App\Http\Controllers\ManufacturingController@ModelExecutingFilter');
       Route::post('AddExecutingReceiving', 'App\Http\Controllers\ManufacturingController@AddExecutingReceiving');


       //ManufacturingOrder
       Route::get('ManufacturingOrderSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderSechdule');
       Route::get('ManufacturingOrder', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderPage');
       Route::get('ManufacturingOrderFilter', 'App\Http\Controllers\ManufacturingController@ManufacturingOrderFilter');
       Route::post('AddManufacturingOrder', 'App\Http\Controllers\ManufacturingController@AddManufacturingOrder');
       Route::get('DeleteManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingOrder');
       Route::get('EditManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@EditManufacturingOrderPage');
       Route::post('PostEditManufacturingOrder/{id}', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingOrder');
       Route::post('ExchangeGoodsManufacturingOrder', 'App\Http\Controllers\ManufacturingController@ExchangeGoodsManufacturingOrder');
       Route::get('ManufacturingExchangeGoodsPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExchangeGoodsPrint');


       Route::get('EditManufacturingOrder/ClientPhoneAndAddress/{id}', 'App\Http\Controllers\ManufacturingController@ClientPhoneAndAddress');
       Route::get('EditManufacturingOrder/DelegatePhone/{id}', 'App\Http\Controllers\ManufacturingController@DelegatePhone');


       //Manufacturing_Request
       Route::get('Manufacturing_Request_Sechdule', 'App\Http\Controllers\ManufacturingController@Manufacturing_Request_Sechdule');
       Route::get('Manufacturing_Request', 'App\Http\Controllers\ManufacturingController@Manufacturing_RequestPage');
       Route::get('ManufacturingRequestFilter', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestFilter');
       Route::get('ClientPhoneAndAddress/{id}', 'App\Http\Controllers\ManufacturingController@ClientPhoneAndAddress');
       Route::get('DelegatePhone/{id}', 'App\Http\Controllers\ManufacturingController@DelegatePhone');
       Route::get('ManufacturingRequestPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestPrint');
       Route::post('AddManufacturingRequest', 'App\Http\Controllers\ManufacturingController@AddManufacturingRequest');

       Route::get('ApproveManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@ApproveManufacturingRequest');
       Route::get('EndManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@EndManufacturingRequest');
       Route::get('DeleteManufacturingRequest/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingRequest');
       Route::get('EditManufacturingRequest', 'App\Http\Controllers\ManufacturingController@EditManufacturingRequest');
       Route::get('ManufacturingRequestTransferToOrder', 'App\Http\Controllers\ManufacturingController@ManufacturingRequestTransferToOrder');
       Route::post('PostEditManufacturingRequest', 'App\Http\Controllers\ManufacturingController@PostEditManufacturingRequest');
       Route::post('AddTransferManufacturingOrder', 'App\Http\Controllers\ManufacturingController@AddTransferManufacturingOrder');


       //ExchangeManufacturingGoodsSechdule
       Route::get('ExchangeManufacturingGoodsSechdule', 'App\Http\Controllers\ManufacturingController@ExchangeManufacturingGoodsSechdule');
       Route::post('SureExchangeGoodsManufacturingOrder', 'App\Http\Controllers\ManufacturingController@SureExchangeGoodsManufacturingOrder');

       //ManufacturingExecution
       Route::get('ManufacturingExecution/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionPage');
       Route::post('AddManufacturingExecution', 'App\Http\Controllers\ManufacturingController@AddManufacturingExecution');
       Route::get('ManufacturingExecutionPrint/{id}', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionPrint');
       Route::get('ManufacturingExecutionSechdule', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionSechdule');
       Route::get('DeleteManufacturingExecution/{id}', 'App\Http\Controllers\ManufacturingController@DeleteManufacturingExecution');



       //ExaminationsTypes
       Route::get('ExaminationsTypes', 'App\Http\Controllers\ManufacturingController@ExaminationsTypesPage');
       Route::post('AddExaminationsTypes', 'App\Http\Controllers\ManufacturingController@AddExaminationsTypes');
       Route::post('EditExaminationsTypes/{id}', 'App\Http\Controllers\ManufacturingController@EditExaminationsTypes');
       Route::get('DeleteExaminationsTypes/{id}', 'App\Http\Controllers\ManufacturingController@DeleteExaminationsTypes');

       //Quality
       Route::get('ManufacturingExecutionQuality', 'App\Http\Controllers\ManufacturingController@ManufacturingExecutionQuality');
       Route::get('QualitySechdule', 'App\Http\Controllers\ManufacturingController@QualitySechdule');
       Route::get('ExmineFilter/{id}', 'App\Http\Controllers\ManufacturingController@ExmineFilter');
       Route::get('QualityPrint/{id}', 'App\Http\Controllers\ManufacturingController@QualityPrintPage');
       Route::get('DeleteQuality/{id}', 'App\Http\Controllers\ManufacturingController@DeleteQuality');
       Route::get('QualityDone/{id}', 'App\Http\Controllers\ManufacturingController@QualityDone');
       Route::post('AddQuality', 'App\Http\Controllers\ManufacturingController@AddQuality');


 //=====  End Manufacturing ==================================================================================================





// === Owner Reports ========================================================================================================

    //User Log
    Route::get('UserLog', 'App\Http\Controllers\OwnerController@UserLogPage');
    Route::get('UserLogFilter', 'App\Http\Controllers\OwnerController@UserLogFilter');


    //EmpLocations
     Route::get('EmpLocations', 'App\Http\Controllers\OwnerController@EmpLocationsPage');

    //Orders
        Route::get('Orders', 'App\Http\Controllers\OwnerController@OrdersPage');
        Route::get('PendingSales/{id}', 'App\Http\Controllers\OwnerController@PendingSales');
        Route::get('RecivedShipCompSales/{id}', 'App\Http\Controllers\OwnerController@RecivedShipCompSales');
        Route::get('RecivedClientSales/{id}', 'App\Http\Controllers\OwnerController@RecivedClientSales');
        Route::get('PendingPurch/{id}', 'App\Http\Controllers\OwnerController@PendingPurch');
        Route::get('RecivedShipCompPurch/{id}', 'App\Http\Controllers\OwnerController@RecivedShipCompPurch');
        Route::get('RecivedClientPurch/{id}', 'App\Http\Controllers\OwnerController@RecivedClientPurch');

// === End Owner Reports ===================================================================================================


// === Capital ============================================================================================================

    //Capital
    Route::get('Capital', 'App\Http\Controllers\CapitalController@CapitalPage');
    Route::post('AddCapital', 'App\Http\Controllers\CapitalController@AddCapital');

    //Partners
    Route::get('Partners', 'App\Http\Controllers\CapitalController@PartnersPage');
    Route::post('AddPartner', 'App\Http\Controllers\CapitalController@AddPartner');
    Route::get('DeletePartner/{id}', 'App\Http\Controllers\CapitalController@DeletePartner');

    //Spend_Profits
    Route::get('Spend_Profits', 'App\Http\Controllers\CapitalController@Spend_ProfitsPage');
    Route::get('PartnerFilter/{id}', 'App\Http\Controllers\CapitalController@PartnerFilter');
    Route::get('PrintSpendProfits/{id}', 'App\Http\Controllers\CapitalController@PrintSpendProfits');
    Route::post('AddSpendProfit', 'App\Http\Controllers\CapitalController@AddSpendProfit');

    //Branches
    Route::get('Branches', 'App\Http\Controllers\CapitalController@BranchesPage');
    Route::post('AddBranches', 'App\Http\Controllers\CapitalController@AddBranches');
    Route::post('EditBranches/{id}', 'App\Http\Controllers\CapitalController@EditBranches');
    Route::get('DeleteBranches/{id}', 'App\Http\Controllers\CapitalController@DeleteBranches');
// === End Capital ========================================================================================================


// === Secretariat ========================================================================================================

        //Secretariat_Stores
     Route::get('Secretariat_Stores', 'App\Http\Controllers\SecretariatController@Secretariat_StoresPage');
     Route::post('AddSecretariat_Stores', 'App\Http\Controllers\SecretariatController@AddSecretariat_Stores');
     Route::post('EditSecretariat_Stores/{id}', 'App\Http\Controllers\SecretariatController@EditSecretariat_Stores');
     Route::get('DeleteSecretariat_Stores/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Stores');

        //Secretariat_Import_goods
          Route::get('Secretariat_Import_goods', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goodsPage');
          Route::get('ImportGoodsProductsFilter', 'App\Http\Controllers\SecretariatController@ImportGoodsProductsFilter');
          Route::post('AddSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@AddSecretariat_Import_goods');
          Route::get('Secretariat_Import_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goodsPrint');
          Route::get('Secretariat_Import_goods_Sechdule', 'App\Http\Controllers\SecretariatController@Secretariat_Import_goods_Sechdule');
          Route::get('EditSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@EditSecretariat_Import_goods');
          Route::get('DeleteSecretariat_Import_goods/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Import_goods');
          Route::post('PostEditSecretariat_Import_goods', 'App\Http\Controllers\SecretariatController@PostEditSecretariat_Import_goods');

        //Secretariat_Export_goods
          Route::get('Secretariat_Export_goods', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goodsPage');
          Route::get('ExportProductsFilter', 'App\Http\Controllers\SecretariatController@ExportProductsFilter');
          Route::post('AddSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@AddSecretariatExportGoods');
          Route::get('Secretariat_Export_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goodsPrint');
          Route::get('Secretariat_Export_goods_Sechdule', 'App\Http\Controllers\SecretariatController@Secretariat_Export_goods_Sechdule');
          Route::get('EditSecretariat_Export_goods', 'App\Http\Controllers\SecretariatController@EditSecretariat_Export_goods');
          Route::get('DeleteSecretariat_Export_goods/{id}', 'App\Http\Controllers\SecretariatController@DeleteSecretariat_Export_goods');
          Route::post('PostEditSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@PostEditSecretariatExportGoods');
          //RecivedSecretariat_Export_goods
          Route::get('RecivedSecretariat_Export_goods', 'App\Http\Controllers\SecretariatController@RecivedSecretariat_Export_goods');
          Route::post('PostRecivedSecretariatExportGoods', 'App\Http\Controllers\SecretariatController@PostRecivedSecretariatExportGoods');
          Route::get('Secretariat_Recived_Export_goodsPrint/{id}', 'App\Http\Controllers\SecretariatController@Secretariat_Recived_Export_goodsPrint');


          //Secretariat_Stores_Qty
          Route::get('Secretariat_Stores_Qty', 'App\Http\Controllers\SecretariatController@Secretariat_Stores_Qty');
          Route::get('Secretariat_Stores_QtyFilter', 'App\Http\Controllers\SecretariatController@Secretariat_Stores_QtyFilter');

        //ManufacturingModelSecretariat
         Route::get('ManufacturingModelSecretariatSechdule', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariatSechdule');
         Route::get('ManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariat');
       Route::post('AddManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@AddManufacturingModelSecretariat');
       Route::get('IncomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilter');
       Route::get('EditManufacturingSecretariatModel/IncomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilter');
       Route::get('OutcomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilter');
       Route::get('EditManufacturingSecretariatModel/OutcomManufacturingModelSecretariatFilter', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilter');
       Route::get('DeleteManufacturingModelSecretariat/{id}', 'App\Http\Controllers\SecretariatController@DeleteManufacturingModelSecretariat');
       Route::get('EditManufacturingSecretariatModel/{id}', 'App\Http\Controllers\SecretariatController@EditManufacturingSecretariatModel');
       Route::post('PostEditManufacturingModelSecretariat', 'App\Http\Controllers\SecretariatController@PostEditManufacturingModelSecretariat');
       Route::get('ManuSecrtaritExecution/{id}', 'App\Http\Controllers\SecretariatController@ManuSecrtaritExecution');


        //ManufacturingModelSecretariatPrecent
           Route::get('ManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@ManufacturingModelSecretariatPrecent');
           Route::post('AddManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@AddManufacturingModelSecretariatPrecent');
           Route::get('IncomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilterPrecent');
           Route::get('OutcomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilterPrecent');

            Route::get('EditManufacturingModelSecretariatPrecent/IncomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@IncomManufacturingModelSecretariatFilterPrecent');
            Route::get('EditManufacturingModelSecretariatPrecent/OutcomManufacturingModelSecretariatFilterPrecent', 'App\Http\Controllers\SecretariatController@OutcomManufacturingModelSecretariatFilterPrecent');

           Route::get('EditManufacturingModelSecretariatPrecent/{id}', 'App\Http\Controllers\SecretariatController@EditManufacturingModelSecretariatPrecent');
              Route::post('PostEditManufacturingModelSecretariatPrecent', 'App\Http\Controllers\SecretariatController@PostEditManufacturingModelSecretariatPrecent');

        //ExecutingReceivingSecretariat
             Route::get('ExecutingReceivingSecretariat', 'App\Http\Controllers\SecretariatController@ExecutingReceivingSecretariat');
             Route::get('ModelExecutingReceivingSecretariatFilter', 'App\Http\Controllers\SecretariatController@ModelExecutingReceivingSecretariatFilter');
             Route::get('ManuSecrtaritExecution/{id}/ModelExecutingReceivingSecretariatFilter', 'App\Http\Controllers\SecretariatController@ModelExecutingReceivingSecretariatFilter');
             Route::post('AddExecutingReceivingSecretariat', 'App\Http\Controllers\SecretariatController@AddExecutingReceivingSecretariat');


          // === End Secretariat ======================================================================================================

          // === Petrol =============================================================================================================

          // == CompanyCars ==
          Route::get('CompanyCars', 'App\Http\Controllers\PetrolController@CompanyCarsPage');
          Route::post('AddCompanyCars', 'App\Http\Controllers\PetrolController@AddCompanyCars');
          Route::post('EditCompanyCars/{id}', 'App\Http\Controllers\PetrolController@EditCompanyCars');
          Route::get('DeleteCompanyCars/{id}', 'App\Http\Controllers\PetrolController@DeleteCompanyCars');

          // == BonesType ==
          Route::get('BonesType', 'App\Http\Controllers\PetrolController@BonesTypePage');
          Route::post('AddBonesType', 'App\Http\Controllers\PetrolController@AddBonesType');
          Route::post('EditBonesType/{id}', 'App\Http\Controllers\PetrolController@EditBonesType');
          Route::get('DeleteBonesType/{id}', 'App\Http\Controllers\PetrolController@DeleteBonesType');

          // == ReciptsType ==
          Route::get('ReciptsType', 'App\Http\Controllers\PetrolController@ReciptsTypePage');
          Route::post('AddReciptsType', 'App\Http\Controllers\PetrolController@AddReciptsType');
          Route::post('EditReciptsType/{id}', 'App\Http\Controllers\PetrolController@EditReciptsType');
          Route::get('DeleteReciptsType/{id}', 'App\Http\Controllers\PetrolController@DeleteReciptsType');


          // == CountersType ==
          Route::get('CountersType', 'App\Http\Controllers\PetrolController@CountersTypePage');
          Route::post('AddCountersType', 'App\Http\Controllers\PetrolController@AddCountersType');
          Route::post('EditCountersType/{id}', 'App\Http\Controllers\PetrolController@EditCountersType');
          Route::get('DeleteCountersType/{id}', 'App\Http\Controllers\PetrolController@DeleteCountersType');

          //PurchasePetrol
          Route::get('PurchasePetrol', 'App\Http\Controllers\PetrolController@PurchasePetrolPage');
          Route::get('PurchacesPetrolFilter', 'App\Http\Controllers\PetrolController@PurchacesPetrolFilter');
          Route::post('AddPurchasesPetrol', 'App\Http\Controllers\PetrolController@AddPurchasesPetrol');
          Route::get('PurchPetrolPrint/{id}', 'App\Http\Controllers\PetrolController@PurchPetrolPrint');
          Route::get('PurchasePetrolSechdule', 'App\Http\Controllers\PetrolController@PurchasePetrolSechdule');
          Route::get('DeletePurchasePetrol/{id}', 'App\Http\Controllers\PetrolController@DeletePurchasePetrol');
          Route::get('EditPurchPetrol', 'App\Http\Controllers\PetrolController@EditPurchPetrolPage');
          Route::post('PostEditPurchasesPetrol', 'App\Http\Controllers\PetrolController@PostEditPurchasesPetrol');

          //SalesPetrol
          Route::get('SalesPetrol', 'App\Http\Controllers\PetrolController@SalesPetrolPage');
          Route::get('SalesPetrolSechdule', 'App\Http\Controllers\PetrolController@SalesPetrolSechdule');
          Route::get('SalesPetrolPrint/{id}', 'App\Http\Controllers\PetrolController@SalesPetrolPrint');
          Route::get('ConsumptionFilter', 'App\Http\Controllers\PetrolController@ConsumptionFilter');
          Route::get('CounterDataFilter', 'App\Http\Controllers\PetrolController@CounterDataFilter');
          Route::get('StoreCounterFilter', 'App\Http\Controllers\PetrolController@StoreCounterFilter');
          Route::post('AddSalesPetrol', 'App\Http\Controllers\PetrolController@AddSalesPetrol');
          Route::get('DeleteSalesPetrol/{id}', 'App\Http\Controllers\PetrolController@DeleteSalesPetrol');
          Route::get('EditSalesPetrol', 'App\Http\Controllers\PetrolController@EditSalesPetrol');
          Route::post('PostEditSalesPetrol', 'App\Http\Controllers\PetrolController@PostEditSalesPetrol');





// === End Petrol =========================================================================================================


// === Website =========================================================================================================


    //WebSlider
                    Route::get('WebSlider', 'App\Http\Controllers\WebsiteController@WebSliderPage');
                    Route::post('AddWebSlider', 'App\Http\Controllers\WebsiteController@AddWebSlider');
                    Route::post('EditWebSlider/{id}', 'App\Http\Controllers\WebsiteController@EditWebSlider');
                    Route::get('DeleteWebSlider/{id}', 'App\Http\Controllers\WebsiteController@DeleteWebSlider');
                    Route::get('UnActiveSlider/{id}', 'App\Http\Controllers\WebsiteController@UnActiveSlider');
                    Route::get('ActiveSlider/{id}', 'App\Http\Controllers\WebsiteController@ActiveSlider');

     //About
                    Route::get('About', 'App\Http\Controllers\WebsiteController@AboutPage');
                    Route::post('UpdateAbout/{id}', 'App\Http\Controllers\WebsiteController@UpdateAbout');

       //SocialMedia
            Route::get('SocialMedia', 'App\Http\Controllers\WebsiteController@SocialMediaPage');
            Route::post('SocialMediaUpdate/{id}', 'App\Http\Controllers\WebsiteController@SocialMediaUpdate');


         //MsgRqst
                    Route::get('MsgRqst', 'App\Http\Controllers\WebsiteController@MsgRqstPage');
                    Route::get('DeleteMsgRqst/{id}', 'App\Http\Controllers\WebsiteController@DeleteMsgRqst');

            //ContactUS
            Route::get('ContactUS', 'App\Http\Controllers\WebsiteController@ContactUSPage');
            Route::post('ContactUSUpdate/{id}', 'App\Http\Controllers\WebsiteController@ContactUSUpdate');

        //Articles
                    Route::get('Articles', 'App\Http\Controllers\WebsiteController@ArticlesPage');
                    Route::post('AddArticles', 'App\Http\Controllers\WebsiteController@AddArticles');
                    Route::post('EditArticles/{id}', 'App\Http\Controllers\WebsiteController@EditArticles');
                    Route::get('DeleteArticles/{id}', 'App\Http\Controllers\WebsiteController@DeleteArticles');

         //Polices
                    Route::get('Polices', 'App\Http\Controllers\WebsiteController@PolicesPage');
                    Route::post('UpdatePolices/{id}', 'App\Http\Controllers\WebsiteController@UpdatePolices');


               //Terms
                    Route::get('Terms', 'App\Http\Controllers\WebsiteController@TermsPage');
                    Route::post('UpdateTerms/{id}', 'App\Http\Controllers\WebsiteController@UpdateTerms');

                   // CouponCode
    Route::get('CouponCode', 'App\Http\Controllers\WebsiteController@CouponCodePage');
    Route::post('AddCouponCode', 'App\Http\Controllers\WebsiteController@AddCouponCode');
    Route::post('EditCouponCode/{id}', 'App\Http\Controllers\WebsiteController@EditCouponCode');
    Route::get('DeleteCouponCode/{id}', 'App\Http\Controllers\WebsiteController@DeleteCouponCode');

            //FAQ
                    Route::get('FAQ', 'App\Http\Controllers\WebsiteController@FAQPage');
                    Route::post('AddFAQ', 'App\Http\Controllers\WebsiteController@AddFAQ');
                    Route::post('EditFAQ/{id}', 'App\Http\Controllers\WebsiteController@EditFAQ');
                    Route::get('DeleteFAQ/{id}', 'App\Http\Controllers\WebsiteController@DeleteFAQ');

            //Countris
                    Route::get('Countris', 'App\Http\Controllers\WebsiteController@CountrisPage');
                    Route::post('AddCountris', 'App\Http\Controllers\WebsiteController@AddCountris');
                    Route::post('EditCountris/{id}', 'App\Http\Controllers\WebsiteController@EditCountris');
                    Route::get('DeleteCountris/{id}', 'App\Http\Controllers\WebsiteController@DeleteCountris');


            //ProDetailsImg
                    Route::get('ProDetailsImg', 'App\Http\Controllers\WebsiteController@ProDetailsImg');
                    Route::post('EditProDetailsImg/{id}', 'App\Http\Controllers\WebsiteController@EditProDetailsImg');


            //BefroeFooter
                    Route::get('BefroeFooter', 'App\Http\Controllers\WebsiteController@BefroeFooter');
                    Route::post('EditBefroeFooter/{id}', 'App\Http\Controllers\WebsiteController@EditBefroeFooter');

//ShopOrders
    Route::get('ShopOrders', 'App\Http\Controllers\WebsiteController@ShopOrders');
    Route::post('ChangeStatusShop', 'App\Http\Controllers\WebsiteController@ChangeStatusShop');








 // === End Website =========================================================================================================


 // === Shipping =========================================================================================================

          //Shipping Type
    Route::get('ShippingType', 'App\Http\Controllers\ShippingController@ShippingTypePage');
    Route::post('AddShippingType', 'App\Http\Controllers\ShippingController@AddShippingType');
    Route::post('EditShippingType/{id}', 'App\Http\Controllers\ShippingController@EditShippingType');
    Route::get('DeleteShippingType/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingType');

            //Shipping Status
    Route::get('ShippingStatus', 'App\Http\Controllers\ShippingController@ShippingStatusPage');
    Route::post('AddShippingStatus', 'App\Http\Controllers\ShippingController@AddShippingStatus');
    Route::post('EditShippingStatus/{id}', 'App\Http\Controllers\ShippingController@EditShippingStatus');
    Route::get('DeleteShippingStatus/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingStatus');

    //Shipping Order
    Route::get('ShippingOrder', 'App\Http\Controllers\ShippingController@ShippingOrderPage');
    Route::get('ClientPlaceFilter/{client}/{weight}/{address}', 'App\Http\Controllers\ShippingController@ClientPlaceFilter');
    Route::post('AddShippingOrder', 'App\Http\Controllers\ShippingController@AddShippingOrder');
    Route::post('EditShippingOrder/{id}', 'App\Http\Controllers\ShippingController@EditShippingOrder');
    Route::get('DeleteShippingOrder/{id}', 'App\Http\Controllers\ShippingController@DeleteShippingOrder');
     Route::get('AddressFilter/{id}', 'App\Http\Controllers\ShippingController@AddressFilter');
     Route::get('SureCustomerCollection/{id}', 'App\Http\Controllers\ShippingController@SureCustomerCollection');
     Route::post('VendorCollectShipping', 'App\Http\Controllers\ShippingController@VendorCollectShipping');
     Route::post('CustomerCollectionRequests', 'App\Http\Controllers\ShippingController@CustomerCollectionRequests');


        //My Orders
    Route::get('MyOrdersEmp', 'App\Http\Controllers\ShippingController@MyOrdersEmpPage');
    Route::post('ChangeStatusOrder', 'App\Http\Controllers\ShippingController@ChangeStatusOrder');
    Route::post('CustomerCollection', 'App\Http\Controllers\ShippingController@CustomerCollection');
    Route::get('PrintShip/{id}', 'App\Http\Controllers\ShippingController@PrintShip');
    Route::post('CancelOrder', 'App\Http\Controllers\ShippingController@CancelOrder');
    Route::post('RequestDone', 'App\Http\Controllers\ShippingController@RequestDone');



    //Report My Orders
     Route::get('ReportMyOrdersEmp', 'App\Http\Controllers\ShippingController@ReportMyOrdersEmpPage');
     Route::get('FilterReportMyOrders', 'App\Http\Controllers\ShippingController@FilterReportMyOrders');



    //Vendor Collections
    Route::get('VendorCoolections', 'App\Http\Controllers\ShippingController@VendorCoolectionsPage');
    Route::post('PostVendorCollection', 'App\Http\Controllers\ShippingController@PostVendorCollection');

    //ReportOrders
      Route::get('ReportOrders', 'App\Http\Controllers\ShippingController@ReportOrdersPage');
      Route::get('FilterOrders', 'App\Http\Controllers\ShippingController@FilterOrders');


 // === End Shipping =========================================================================================================




 // === Electonic Bill =========================================================================================================


//Send_Bill_Sales
        Route::get('Send_Bill_Sales', 'App\Http\Controllers\ElectronicBillController@Send_Bill_Sales');
        Route::get('Send_Bill_ReturnSales', 'App\Http\Controllers\ElectronicBillController@Send_Bill_ReturnSales');
        Route::get('Send_Bill_SalesFilter', 'App\Http\Controllers\ElectronicBillController@Send_Bill_SalesFilter');
        Route::get('ReturnSend_Bill_SalesFilter', 'App\Http\Controllers\ElectronicBillController@ReturnSend_Bill_SalesFilter');

//Bill_Purchases_Sent
          Route::get('Bill_Purchases_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_Purchases_Sent');
//Bill_Sales_Sent
          Route::get('Bill_Sales_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_Sent');
          Route::get('Bill_ReturnSales_Sent', 'App\Http\Controllers\ElectronicBillController@Bill_ReturnSales_Sent');
          Route::get('Bill_Sales_SentFilter', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_SentFilter');
          Route::get('Bill_ReturnSales_SentFilter', 'App\Http\Controllers\ElectronicBillController@Bill_ReturnSales_SentFilter');
          Route::get('Bill_Sales_Sent_Web', 'App\Http\Controllers\ElectronicBillController@Bill_Sales_Sent_Web');
          Route::get('FilterBill_Sales_Sent_Web', 'App\Http\Controllers\ElectronicBillController@FilterBill_Sales_Sent_Web');
          Route::get('printPDFSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@printPDFSalesElectronic');
          Route::get('FilterprintPDFSalesElectronic/{uuid}/{longid}', 'App\Http\Controllers\ElectronicBillController@FilterprintPDFSalesElectronic');
          Route::get('printPDFSalesElectronic/FilterprintPDFSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@FilterprintPDFSalesElectronic');
          Route::get('CancelSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@CancelSalesElectronic');
          Route::get('RejectSalesElectronic/{uuid}', 'App\Http\Controllers\ElectronicBillController@RejectSalesElectronic');

                //SendElectronicBill
         Route::get('postSendSales', 'App\Http\Controllers\ElectronicBillController@postSendSales');
         Route::get('postSendSalesEXP', 'App\Http\Controllers\ElectronicBillController@postSendSalesEXP');
         Route::get('SendSalesBill/{id}', 'App\Http\Controllers\ElectronicBillController@SendSalesBill');
         Route::get('ReturnSendSalesBill/{id}', 'App\Http\Controllers\ElectronicBillController@ReturnSendSalesBill');

 // === End Electronic Bill  =========================================================================================================



 // === Hotels  =========================================================================================================


           //RoomsType
          Route::get('RoomsType', 'App\Http\Controllers\HotelController@RoomsType');
          Route::post('AddRoomsType', 'App\Http\Controllers\HotelController@AddRoomsType');
          Route::post('EditRoomsType/{id}', 'App\Http\Controllers\HotelController@EditRoomsType');
          Route::get('DeleteRoomsType/{id}', 'App\Http\Controllers\HotelController@DeleteRoomsType');

        //Rooms
          Route::get('Rooms', 'App\Http\Controllers\HotelController@Rooms');
          Route::post('AddRooms', 'App\Http\Controllers\HotelController@AddRooms');
          Route::post('EditRooms/{id}', 'App\Http\Controllers\HotelController@EditRooms');
          Route::get('DeleteRooms/{id}', 'App\Http\Controllers\HotelController@DeleteRooms');


//Reservations
          Route::get('Reservations', 'App\Http\Controllers\HotelController@Reservations');
          Route::get('RoomsFilter', 'App\Http\Controllers\HotelController@RoomsFilter');
          Route::get('NewClientHotel', 'App\Http\Controllers\HotelController@NewClientHotel');
          Route::get('EditReserv/NewClientHotel', 'App\Http\Controllers\HotelController@NewClientHotel');
          Route::get('EditReserv/{id}/RoomsFilter', 'App\Http\Controllers\HotelController@RoomsFilter');
          Route::get('EditReserv/AccountBalanceSOFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
          Route::get('EditReserv/AccountBalanceFilter/{id}', 'App\Http\Controllers\PurchasesController@AccountBalanceFilter');
          Route::post('AddResrvation', 'App\Http\Controllers\HotelController@AddResrvation');
          Route::post('PostEditResrvation', 'App\Http\Controllers\HotelController@PostEditResrvation');
          Route::get('Reservations_Sechdule', 'App\Http\Controllers\HotelController@Reservations_Sechdule');
         Route::get('EditReserv/AllEmps', 'App\Http\Controllers\AccountsController@AllEmps');
         Route::get('EditReserv/AllEmpsJ/{id}', 'App\Http\Controllers\AccountsController@AllEmpsJ');
           Route::get('EditReserv/{id}', 'App\Http\Controllers\HotelController@EditReserv');
           Route::get('DeleteReservation/{id}', 'App\Http\Controllers\HotelController@DeleteReservation');
           Route::get('CheckoutReservation/{id}', 'App\Http\Controllers\HotelController@CheckoutReservation');

        //ReservationsReport
         Route::get('ReservationsReport', 'App\Http\Controllers\HotelController@ReservationsReport');
         Route::get('ReservReportFilterrr', 'App\Http\Controllers\HotelController@ReservReportFilterrr');
 // === End Hotels  =========================================================================================================

          //Imports
          Route::get('ExportProducts', 'App\Http\Controllers\StoresController@ExportProductsPage');
          Route::post('ImportProducts', 'App\Http\Controllers\StoresController@ImportProducts');
          Route::post('ImportProductsWithStart', 'App\Http\Controllers\StoresController@ImportProductsWithStart');
          Route::post('ImportProductsUnits', 'App\Http\Controllers\StoresController@ImportProductsUnits');
          Route::post('ImportGroups', 'App\Http\Controllers\StoresController@ImportGroups');
          Route::post('ImportMoves', 'App\Http\Controllers\StoresController@ImportMoves');
          Route::post('ImportStartPeriod', 'App\Http\Controllers\StoresController@ImportStartPeriod');
          Route::post('ImportProStartPeriod', 'App\Http\Controllers\StoresController@ImportProStartPeriod');
          Route::post('ImportClients', 'App\Http\Controllers\StoresController@ImportClients');
          Route::post('ImportVendors', 'App\Http\Controllers\StoresController@ImportVendors');
          Route::post('ImportEmployees', 'App\Http\Controllers\StoresController@ImportEmployees');
          Route::post('ImportReceipt_Voucher', 'App\Http\Controllers\StoresController@ImportReceipt_Voucher');
          Route::post('ImportPayment_Voucher', 'App\Http\Controllers\StoresController@ImportPayment_Voucher');
          Route::post('ImportProductRepeat', 'App\Http\Controllers\StoresController@ImportProductRepeat');
          Route::post('ImportCustomersGroup', 'App\Http\Controllers\StoresController@ImportCustomersGroup');
          Route::post('ImportBrands', 'App\Http\Controllers\StoresController@ImportBrands');

        //Export

          Route::get('ExportStoresQtyyy', 'App\Http\Controllers\StoresController@ExportStoresQtyyy');
          Route::get('ExportStoresQtyyyRased', 'App\Http\Controllers\StoresController@ExportStoresQtyyyRased');
          Route::get('ExportStoresBalancesNew', 'App\Http\Controllers\StoresController@ExportStoresBalancesNew');
          Route::get('ExportStoresBalancesCat', 'App\Http\Controllers\StoresController@ExportStoresBalancesCat');
          Route::get('ExportDelegateSalesDetails', 'App\Http\Controllers\StoresController@ExportDelegateSalesDetails');
          Route::get('ExportProfitDelegateSalesDetails', 'App\Http\Controllers\StoresController@ExportProfitDelegateSalesDetails');
          Route::get('ExportStoresCosts', 'App\Http\Controllers\StoresController@ExportStoresCosts');
          Route::get('ExportInstallmentCompaniesSales', 'App\Http\Controllers\StoresController@ExportInstallmentCompaniesSales');
          Route::get('ExportPurchasesBillsReport', 'App\Http\Controllers\StoresController@ExportPurchasesBillsReport');
          Route::get('ExportSalesBillsReport', 'App\Http\Controllers\StoresController@ExportSalesBillsReport');
          Route::get('ExportStoresMovesReport', 'App\Http\Controllers\StoresController@ExportStoresMovesReport');
          Route::get('ExportStoresTransferReport', 'App\Http\Controllers\StoresController@ExportStoresTransferReport');
          Route::get('ExportSafesTransferReport', 'App\Http\Controllers\StoresController@ExportSafesTransferReport');
          Route::get('ExportProductMoveDetails', 'App\Http\Controllers\StoresController@ExportProductMoveDetails');
          Route::get('ExportCompareSalesPrice', 'App\Http\Controllers\StoresController@ExportCompareSalesPrice');
          Route::get('ExportMostSalesProducts', 'App\Http\Controllers\StoresController@ExportMostSalesProducts');
          Route::get('ExportProfitSalesProduct', 'App\Http\Controllers\StoresController@ExportProfitSalesProduct');
          Route::get('ExportClientsStatement', 'App\Http\Controllers\StoresController@ExportClientsStatement');
          Route::get('ExportClientAccountStatement', 'App\Http\Controllers\StoresController@ExportClientAccountStatement');
          Route::get('ExportVendorAccountStatement', 'App\Http\Controllers\StoresController@ExportVendorAccountStatement');
          Route::get('ExportVendorsStatement', 'App\Http\Controllers\StoresController@ExportVendorsStatement');
          Route::get('ExportInventorySerial', 'App\Http\Controllers\StoresController@ExportInventorySerial');
          Route::post('ExportSalesCustomersGroupsFilter', 'App\Http\Controllers\StoresController@ExportSalesCustomersGroupsFilter');



    });



});

    });

     });


});

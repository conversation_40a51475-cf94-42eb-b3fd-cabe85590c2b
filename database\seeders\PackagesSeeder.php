<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Packages;

class PackagesSeeder extends Seeder
{
    /**
     * Run the database seeder.
     *
     * @return void
     */
    public function run()
    {
        // Create default packages
        Packages::create([
            'Arabic_Name' => 'الباقة الأساسية',
            'English_Name' => 'Basic Package'
        ]);

        Packages::create([
            'Arabic_Name' => 'الباقة المتقدمة',
            'English_Name' => 'Advanced Package'
        ]);

        Packages::create([
            'Arabic_Name' => 'الباقة الاحترافية',
            'English_Name' => 'Professional Package'
        ]);
    }
}

<?php $__env->startSection('content'); ?>

<title><?php echo e(trans('admin.Education')); ?></title>
       


                    <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo e(url('OstAdmin')); ?>"><?php echo e(trans('admin.Dashboard')); ?></a></li>
                            <li class="breadcrumb-item"><?php echo e(trans('admin.Education')); ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
                        <div class="subheader">
                            <h1 class="subheader-title">
                                <i class='fal fa-info-circle'></i> <?php echo e(trans('admin.Education')); ?>

                            </h1>
                        </div>
                        

                        
                    <?php $__currentLoopData = $Videos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $edu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                        <div class="fs-lg fw-300 p-5 bg-white border-faded rounded mb-g">
                        <div class="row">
                        <div class="col-md-4">
                          <?php echo e(app()->getLocale() == 'ar' ?$edu->Arabic_Name :$edu->English_Name); ?>  
                        </div>
                           <div class="col-md-8">
                                     
                 <iframe width="560" height="315" src="<?php echo e($edu->Video); ?>" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
                                                    
                            </div>     
                        </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                      
      
                    </main>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp2\htdocs\new_ost\resources\views/admin/RabihEducation.blade.php ENDPATH**/ ?>
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRabihEducationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rabih_education', function (Blueprint $table) {
            $table->id();
            $table->string('Arabic_Name')->nullable();
            $table->string('English_Name')->nullable();
            $table->text('Video')->nullable();
            $table->unsignedBigInteger('Package')->nullable();
            $table->timestamps();

            $table->foreign('Package')->references('id')->on('packages')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rabih_education');
    }
}
